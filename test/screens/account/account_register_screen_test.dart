import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/screens/account/account_register_screen.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';
import 'package:uuid/uuid.dart';

import 'account_register_screen_test.mocks.dart';

@GenerateMocks([AccountViewModel])
void main() {
  late MockAccountViewModel mockViewModel;
  final getIt = GetIt.instance;
  const String accountNumber = '1000';
  const Account testAccount = Account(
    accountNumber: accountNumber,
    accountName: 'Cash Account',
    accountType: 'asset',
    companyId: 1,
  );

  setUp(() {
    // Arrange: Initialize mock and register with GetIt
    mockViewModel = MockAccountViewModel();
    if (getIt.isRegistered<AccountViewModel>()) {
      getIt.unregister<AccountViewModel>();
    }
    getIt.registerSingleton<AccountViewModel>(mockViewModel);

    // Configure default mock behavior
    when(
      mockViewModel.getAccountByNumber(accountNumber),
    ).thenAnswer((_) async => testAccount);
  });

  tearDown(() {
    // Clean up: Unregister mock after each test
    if (getIt.isRegistered<AccountViewModel>()) {
      getIt.unregister<AccountViewModel>();
    }
  });

  testWidgets('AccountRegisterScreen shows loading indicator initially', (
    tester,
  ) async {
    // Arrange: Configure mock to return empty ledger entries
    when(
      mockViewModel.getGeneralLedgerEntriesByAccount(accountNumber),
    ).thenAnswer((_) async => []);

    // Act: Build the widget
    await tester.pumpWidget(
      const MaterialApp(home: AccountRegisterScreen(account: testAccount)),
    );

    // Assert: Verify loading indicator is shown
    expect(find.byType(CircularProgressIndicator), findsOneWidget);

    // Clean up: Wait for any pending operations
    await tester.pumpAndSettle();
  });

  testWidgets('AccountRegisterScreen shows ledger entries when loaded', (
    tester,
  ) async {
    // Arrange: Configure mock with test ledger entries
    final ledgerEntries = [
      GeneralLedger(
        ledgerId: 1,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 15),
        accountNumber: accountNumber,
        description: 'Cash deposit',
        debit: 1000.0,
        credit: 0.0,
        currencyCode: 'USD',
        projectId: 1,
        staffId: 1,
        taxAmount: 10.0,
        companyId: 1,
      ),
      GeneralLedger(
        ledgerId: 2,
        transactionId: const Uuid().v4(),
        transactionDate: DateTime(2023, 1, 20),
        accountNumber: accountNumber,
        description: 'ATM withdrawal',
        debit: 0.0,
        credit: 500.0,
        currencyCode: 'USD',
        projectId: 2,
        staffId: 2,
        taxAmount: 5.0,
        companyId: 1,
      ),
    ];

    when(
      mockViewModel.getGeneralLedgerEntriesByAccount(accountNumber),
    ).thenAnswer((_) async => ledgerEntries);
    when(
      mockViewModel.calculateAccountBalance(accountNumber),
    ).thenAnswer((_) async => 500.0);

    // Act: Build widget and wait for data to load
    await tester.pumpWidget(
      const MaterialApp(home: AccountRegisterScreen(account: testAccount)),
    );
    await tester.pumpAndSettle();

    // Assert: Verify all ledger information is displayed
    expect(find.text('Account: Cash Account'), findsOneWidget);
    expect(find.text('Account #: 1000'), findsOneWidget);
    expect(find.text('Current Balance:'), findsOneWidget);
    expect(find.text('\$500.00'), findsOneWidget);
    expect(find.text('Deposit - Cash deposit'), findsOneWidget);
    expect(find.text('Withdrawal - ATM withdrawal'), findsOneWidget);
    expect(find.text('Dr: \$1000.00'), findsOneWidget);
    expect(find.text('Cr: \$500.00'), findsOneWidget);
  });

  testWidgets('AccountRegisterScreen shows error when loading account fails', (
    tester,
  ) async {
    // Arrange: Configure mock to throw error
    when(
      mockViewModel.getAccountByNumber(any),
    ).thenThrow(BusinessException('Unable to fetch account'));

    // Act: Build widget and wait for error state
    await tester.pumpWidget(
      const MaterialApp(
        home: AccountRegisterScreen(
          account: Account(
            accountNumber: '1000',
            accountName: 'Test Account',
            accountType: 'asset',
            companyId: 1,
          ),
        ),
      ),
    );
    await tester.pumpAndSettle();

    // Assert: Verify error message and retry button are shown
    expect(
      find.text('Error: BusinessException: Unable to fetch account'),
      findsOneWidget,
    );
    expect(find.text('Retry'), findsOneWidget);
  });

  testWidgets(
    'AccountRegisterScreen shows error when loading ledger entries fails',
    (tester) async {
      // Arrange: Configure mock to throw error
      when(
        mockViewModel.getGeneralLedgerEntriesByAccount(any),
      ).thenThrow(BusinessException('Unable to load ledger entries'));

      // Act: Build widget and wait for error state
      await tester.pumpWidget(
        const MaterialApp(
          home: AccountRegisterScreen(
            account: Account(
              accountNumber: '1000',
              accountName: 'Test Account',
              accountType: 'asset',
              companyId: 1,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert: Verify error message and retry button are shown
      expect(
        find.text('Error: BusinessException: Unable to load ledger entries'),
        findsOneWidget,
      );
      expect(find.text('Retry'), findsOneWidget);
    },
  );

  testWidgets('AccountRegisterScreen shows empty state when no entries', (
    tester,
  ) async {
    // Arrange: Configure mock to return empty ledger entries
    when(
      mockViewModel.getGeneralLedgerEntriesByAccount(accountNumber),
    ).thenAnswer((_) async => []);
    when(
      mockViewModel.calculateAccountBalance(accountNumber),
    ).thenAnswer((_) async => 0.0);

    // Act: Build widget and wait for data to load
    await tester.pumpWidget(
      const MaterialApp(home: AccountRegisterScreen(account: testAccount)),
    );
    await tester.pumpAndSettle();

    // Assert: Verify empty state message is shown
    expect(
      find.text('No transactions found for this account.'),
      findsOneWidget,
    );
  });

  testWidgets('Retry button reloads ledger entries when pressed', (
    tester,
  ) async {
    // Arrange: Configure mock with failing then succeeding behavior
    reset(mockViewModel);
    int callCount = 0;

    when(
      mockViewModel.getAccountByNumber(any),
    ).thenAnswer((_) async => testAccount);
    when(mockViewModel.getGeneralLedgerEntriesByAccount(any)).thenAnswer((_) {
      if (callCount == 0) {
        callCount++;
        throw BusinessException('Unable to load ledger entries');
      } else {
        return Future.value([
          GeneralLedger(
            ledgerId: 1,
            transactionId: '1',
            transactionDate: DateTime(2024, 1, 1),
            accountNumber: '1000',
            description: 'Cash deposit',
            debit: 1000.0,
            credit: 0.0,
            currencyCode: 'USD',
            companyId: 1,
          ),
        ]);
      }
    });
    when(
      mockViewModel.calculateAccountBalance(accountNumber),
    ).thenAnswer((_) async => 1000.0);

    // Act: Build widget and trigger retry
    await tester.pumpWidget(
      const MaterialApp(home: AccountRegisterScreen(account: testAccount)),
    );

    // Wait for initial load to fail
    await tester.pumpAndSettle();

    // Verify error state is shown
    expect(
      find.text('Error: BusinessException: Unable to load ledger entries'),
      findsOneWidget,
    );

    // Tap retry button and wait for rebuild
    await tester.tap(find.text('Retry'));

    // Wait for the widget to rebuild and data to load
    await tester.pump();
    await tester.pumpAndSettle();

    // Assert: Verify success state and ledger entry is shown
    expect(find.text('Deposit - Cash deposit'), findsOneWidget);
    expect(find.text('Dr: \$1000.00'), findsOneWidget);
  });
}
