import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/screens/account/account_list_screen_refactored.dart';
import 'package:we_like_money/screens/account/components/account_list_item.dart';
import 'package:we_like_money/screens/account/components/empty_accounts_view.dart';
import 'package:we_like_money/screens/account/components/error_view.dart';
import 'package:we_like_money/screens/account/components/loading_view.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

import 'account_list_screen_test.mocks.dart';

@GenerateMocks([AccountViewModel])
void main() {
  late MockAccountViewModel mockViewModel;
  final getIt = GetIt.instance;

  setUp(() {
    // Arrange: Initialize mock and register with GetIt
    mockViewModel = MockAccountViewModel();
    if (getIt.isRegistered<AccountViewModel>()) {
      getIt.unregister<AccountViewModel>();
    }
    getIt.registerSingleton<AccountViewModel>(mockViewModel);
  });

  tearDown(() {
    // Clean up: Unregister mock after each test
    if (getIt.isRegistered<AccountViewModel>()) {
      getIt.unregister<AccountViewModel>();
    }
  });

  testWidgets('AccountListScreenRefactored shows loading indicator initially', (
    tester,
  ) async {
    // Arrange: Configure mock to return empty list
    when(mockViewModel.getAccounts()).thenAnswer((_) async => []);

    // Act: Build the widget
    await tester.pumpWidget(
      const MaterialApp(home: AccountListScreenRefactored()),
    );

    // Assert: Verify loading indicator is shown
    expect(find.byType(LoadingView), findsOneWidget);

    // Clean up: Wait for any pending operations
    await tester.pumpAndSettle();
  });

  testWidgets('AccountListScreenRefactored shows accounts when loaded', (
    tester,
  ) async {
    // Arrange: Configure mock with test accounts and balances
    final accounts = [
      const Account(
        accountName: 'Test Account 1',
        accountNumber: '1000',
        accountType: 'asset',
        companyId: 1,
      ),
      const Account(
        accountName: 'Test Account 2',
        accountNumber: '2000',
        accountType: 'liability',
        companyId: 1,
      ),
    ];

    when(mockViewModel.getAccounts()).thenAnswer((_) async => accounts);
    for (final account in accounts) {
      when(
        mockViewModel.calculateAccountBalance(account.accountNumber),
      ).thenAnswer((_) async => 1000.0);
    }

    // Act: Build widget and wait for data to load
    await tester.pumpWidget(
      const MaterialApp(home: AccountListScreenRefactored()),
    );
    await tester.pumpAndSettle();

    // Assert: Verify all account information is displayed
    expect(find.byType(AccountListItem), findsNWidgets(2));
    expect(find.text('Test Account 1'), findsOneWidget);
    expect(find.text('Test Account 2'), findsOneWidget);
    expect(find.text('Account #: 1000'), findsOneWidget);
    expect(find.text('Account #: 2000'), findsOneWidget);
    expect(find.text('\$1000.00'), findsNWidgets(2));
  });

  testWidgets('AccountListScreenRefactored shows error when loading fails', (
    tester,
  ) async {
    // Arrange: Configure mock to throw error
    when(
      mockViewModel.getAccounts(),
    ).thenThrow(BusinessException('Unable to fetch accounts'));

    // Act: Build widget and wait for error state
    await tester.pumpWidget(
      const MaterialApp(home: AccountListScreenRefactored()),
    );
    await tester.pumpAndSettle();

    // Assert: Verify error view is shown
    expect(find.byType(ErrorView), findsOneWidget);
    expect(find.text('Unable to fetch accounts'), findsOneWidget);
    expect(find.text('Retry'), findsOneWidget);
  });

  testWidgets(
    'AccountListScreenRefactored shows empty state when no accounts',
    (tester) async {
      // Arrange: Configure mock to return empty list
      when(mockViewModel.getAccounts()).thenAnswer((_) async => []);

      // Act: Build widget and wait for data to load
      await tester.pumpWidget(
        const MaterialApp(home: AccountListScreenRefactored()),
      );
      await tester.pumpAndSettle();

      // Assert: Verify empty state message is shown
      expect(find.byType(EmptyAccountsView), findsOneWidget);
    },
  );

  testWidgets('AccountListScreenRefactored filters accounts by search query', (
    tester,
  ) async {
    // Arrange: Configure mock with test accounts and balances
    final accounts = [
      const Account(
        accountName: 'Cash Account',
        accountNumber: '1000',
        accountType: 'asset',
        companyId: 1,
      ),
      const Account(
        accountName: 'Bank Account',
        accountNumber: '2000',
        accountType: 'asset',
        companyId: 1,
      ),
    ];

    when(mockViewModel.getAccounts()).thenAnswer((_) async => accounts);
    for (final account in accounts) {
      when(
        mockViewModel.calculateAccountBalance(account.accountNumber),
      ).thenAnswer((_) async => 1000.0);
    }

    // Act: Build widget and wait for data to load
    await tester.pumpWidget(
      const MaterialApp(home: AccountListScreenRefactored()),
    );
    await tester.pumpAndSettle();

    // Assert: Verify both accounts are shown initially
    expect(find.byType(AccountListItem), findsNWidgets(2));
    expect(find.text('Cash Account'), findsOneWidget);
    expect(find.text('Bank Account'), findsOneWidget);

    // Act: Enter search query
    await tester.enterText(find.byType(TextField), 'Cash');
    await tester.pumpAndSettle();

    // Assert: Verify only matching account is shown
    expect(find.byType(AccountListItem), findsOneWidget);
    expect(find.text('Cash Account'), findsOneWidget);
    expect(find.text('Bank Account'), findsNothing);

    // Act: Clear search query
    await tester.enterText(find.byType(TextField), '');
    await tester.pumpAndSettle();

    // Assert: Verify both accounts are shown again
    expect(find.byType(AccountListItem), findsNWidgets(2));
    expect(find.text('Cash Account'), findsOneWidget);
    expect(find.text('Bank Account'), findsOneWidget);
  });

  testWidgets(
    'AccountListScreenRefactored retries loading when retry button is pressed',
    (tester) async {
      // Arrange: Configure mock to throw error on first call, then succeed
      when(
        mockViewModel.getAccounts(),
      ).thenThrow(BusinessException('Unable to fetch accounts'));

      // Act: Build widget and wait for error state
      await tester.pumpWidget(
        const MaterialApp(home: AccountListScreenRefactored()),
      );
      await tester.pumpAndSettle();

      // Reset mock to return success on next call
      reset(mockViewModel);
      when(mockViewModel.getAccounts()).thenAnswer(
        (_) async => [
          const Account(
            accountName: 'Test Account',
            accountNumber: '1000',
            accountType: 'asset',
            companyId: 1,
          ),
        ],
      );

      when(
        mockViewModel.calculateAccountBalance('1000'),
      ).thenAnswer((_) async => 1000.0);

      // Assert: Verify error view is shown
      expect(find.text('Unable to fetch accounts'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);

      // Act: Tap retry button
      await tester.tap(find.text('Retry'));
      await tester.pump(); // Start the loading state
      await tester.pumpAndSettle(); // Wait for the data to load

      // Assert: Verify success state and mock interactions
      expect(find.byType(AccountListItem), findsOneWidget);
      expect(find.text('Test Account'), findsOneWidget);
      verify(mockViewModel.getAccounts()).called(1);
    },
  );
}
