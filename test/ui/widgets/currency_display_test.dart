import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/models/currency.dart';
import 'package:we_like_money/ui/widgets/currency_display.dart';

void main() {
  group('CurrencyDisplay', () {
    testWidgets('renders correctly with standard currency', (
      WidgetTester tester,
    ) async {
      // Arrange: Create test currency data
      const currency = Currency(currencyCode: 'USD', currencyName: 'US Dollar');

      // Act: Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: CurrencyDisplay(currency: currency)),
        ),
      );

      // Assert: Verify all currency information is displayed correctly
      expect(find.text('USD'), findsOneWidget);
      expect(find.text('US Dollar'), findsOneWidget);
      expect(find.text('U'), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('renders correctly with long currency name', (
      WidgetTester tester,
    ) async {
      // Arrange: Create test currency data with long name
      const currency = Currency(
        currencyCode: 'GBP',
        currencyName: 'British Pound Sterling',
      );

      // Act: Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: CurrencyDisplay(currency: currency)),
        ),
      );

      // Assert: Verify all currency information is displayed correctly
      expect(find.text('GBP'), findsOneWidget);
      expect(find.text('British Pound Sterling'), findsOneWidget);
      expect(find.text('G'), findsOneWidget);
    });

    testWidgets('renders correctly with single character currency code', (
      WidgetTester tester,
    ) async {
      // Arrange: Create test currency data with single character code
      const currency = Currency(
        currencyCode: 'X',
        currencyName: 'Test Currency',
      );

      // Act: Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: CurrencyDisplay(currency: currency)),
        ),
      );

      // Assert: Verify all currency information is displayed correctly
      expect(
        find.text('X'),
        findsNWidgets(2),
      ); // Should appear twice: in circle and as code
      expect(find.text('Test Currency'), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      // Arrange: Create test currency and callback
      const currency = Currency(currencyCode: 'USD', currencyName: 'US Dollar');
      var onTapCalled = false;

      // Act: Build widget and trigger tap
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyDisplay(
              currency: currency,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );
      await tester.tap(find.byType(CurrencyDisplay));

      // Assert: Verify callback was triggered
      expect(onTapCalled, true);
    });

    testWidgets('does not call onTap when not provided', (
      WidgetTester tester,
    ) async {
      // Arrange: Create test currency without onTap
      const currency = Currency(currencyCode: 'USD', currencyName: 'US Dollar');

      // Act: Build widget and trigger tap
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: CurrencyDisplay(currency: currency)),
        ),
      );
      await tester.tap(find.byType(CurrencyDisplay));

      // Assert: Verify no exception is thrown
      expect(tester.takeException(), isNull);
    });

    testWidgets('applies correct theme colors', (WidgetTester tester) async {
      // Arrange: Create test currency and custom theme
      const currency = Currency(currencyCode: 'USD', currencyName: 'US Dollar');
      final theme = ThemeData(
        colorScheme: const ColorScheme.light(
          primary: Colors.blue,
          onPrimary: Colors.white,
        ),
      );

      // Act: Build widget with custom theme
      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: const Scaffold(body: CurrencyDisplay(currency: currency)),
        ),
      );

      // Assert: Verify theme colors are applied
      final container = tester.widget<Container>(
        find.ancestor(of: find.text('U'), matching: find.byType(Container)),
      );
      expect(container.decoration, isA<BoxDecoration>());
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.color, equals(theme.colorScheme.primary));
      expect(decoration.shape, equals(BoxShape.circle));
    });

    testWidgets('has correct layout and spacing', (WidgetTester tester) async {
      // Arrange: Create test currency
      const currency = Currency(currencyCode: 'USD', currencyName: 'US Dollar');

      // Act: Build widget
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
            ),
          ),
          home: const Scaffold(body: CurrencyDisplay(currency: currency)),
        ),
      );

      // Assert: Verify layout properties
      final card = tester.widget<Card>(find.byType(Card).first);
      expect(card, isNotNull);

      // Find the padding widget that wraps the Row
      final padding = tester.widget<Padding>(
        find
            .descendant(
              of: find.byType(InkWell),
              matching: find.byType(Padding),
            )
            .first,
      );
      expect(padding.padding, equals(const EdgeInsets.all(16.0)));

      // Find the container that has the currency code circle using its key
      final container = tester.widget<Container>(
        find.byKey(const Key('currency_code_circle')),
      );

      // Verify container size
      expect(
        container.constraints,
        equals(const BoxConstraints.tightFor(width: 48, height: 48)),
      );

      // Verify container decoration
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.shape, equals(BoxShape.circle));

      // Get the theme from the widget tree
      final theme = Theme.of(tester.element(find.byType(CurrencyDisplay)));
      expect(decoration.color, equals(theme.colorScheme.primary));
    });
  });
}
