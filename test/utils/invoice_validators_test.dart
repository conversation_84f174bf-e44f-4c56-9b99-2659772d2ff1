import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/invoice_validators.dart';

void main() {
  group('InvoiceValidators', () {
    group('invoiceNumber', () {
      test('validates correct invoice numbers', () {
        final validator = InvoiceValidators.invoiceNumber();
        expect(validator('INV-001'), isNull);
        expect(validator('INV_2024_001'), isNull);
        expect(validator('2024/001'), isNull);
        expect(validator('12345'), isNull);
      });

      test('rejects invalid invoice numbers', () {
        final validator = InvoiceValidators.invoiceNumber();
        expect(validator(''), isNotNull); // Required
        expect(validator('INV@001'), isNotNull); // Invalid character
        expect(validator('INV-001!'), isNotNull); // Invalid character
        expect(validator('A' * 51), isNotNull); // Too long (default max 50)
      });

      test('respects custom pattern', () {
        final validator = InvoiceValidators.invoiceNumber(
          pattern: RegExp(r'^[A-Z]{3}-\d{3}$'),
        );
        expect(validator('INV-024'), isNull);
        expect(validator('INV-001'), isNull);
        expect(validator('inv-024'), isNotNull); // Lowercase
        expect(validator('INV024'), isNotNull); // Missing hyphen
      });

      test('respects custom max length', () {
        final validator = InvoiceValidators.invoiceNumber(maxLength: 10);
        expect(validator('INV-001'), isNull);
        expect(validator('INV-2024001'), isNotNull); // Too long
      });

      test('uses custom error message', () {
        final validator = InvoiceValidators.invoiceNumber(
          errorMessage: 'Custom error',
        );
        expect(validator(''), 'Custom error');
      });
    });

    group('dueDateValidator', () {
      final invoiceDate = DateTime(2024, 3, 15);

      test('validates correct due dates', () {
        final validator = InvoiceValidators.dueDateValidator(
          DateTime(2024, 3, 15, 0, 0, 0), // March 15, 00:00:00
          minDays: 1,
          maxDays: 30,
        );
        expect(validator('2024-03-16'), isNull); // 1 day later
        expect(validator('2024-03-20'), isNull); // 5 days later
        expect(validator('2024-04-14'), isNull); // 30 days later
      });

      test('rejects invalid due dates', () {
        final validator = InvoiceValidators.dueDateValidator(
          DateTime(2024, 3, 15, 0, 0, 0), // March 15, 00:00:00
          minDays: 3,
          maxDays: 30,
        );
        expect(validator('2024-03-17'), isNotNull); // March 17 (2 days later)
        expect(validator('2024-04-16'), isNotNull); // April 16 (32 days later)
      });

      test('respects minimum days', () {
        final validator = InvoiceValidators.dueDateValidator(
          invoiceDate,
          minDays: 7,
        );
        expect(validator('2024-03-22'), isNull); // 7 days later
        expect(validator('2024-03-21'), isNotNull); // 6 days later
      });

      test('handles null and empty values', () {
        final validator = InvoiceValidators.dueDateValidator(invoiceDate);
        expect(validator(null), isNull); // Let required validator handle this
        expect(validator(''), isNull); // Let required validator handle this
      });

      test('uses custom error message', () {
        final validator = InvoiceValidators.dueDateValidator(
          invoiceDate,
          minDays: 7,
          errorMessage: 'Custom error',
        );
        expect(validator('2024-03-20'), 'Custom error');
      });
    });

    group('amount', () {
      test('validates correct amounts', () {
        final validator = InvoiceValidators.amount();
        expect(validator('0.01'), isNull);
        expect(validator('100.00'), isNull);
        expect(validator('1,000.00'), isNull);
        expect(validator('\$1,000.00'), isNull);
      });

      test('rejects invalid amounts', () {
        final validator = InvoiceValidators.amount();
        expect(validator(''), isNotNull); // Required
        expect(validator('0.00'), isNotNull); // Zero not allowed by default
        expect(validator('-100.00'), isNotNull); // Negative not allowed
        expect(validator('abc'), isNotNull); // Not a number
      });

      test('respects minimum amount', () {
        final validator = InvoiceValidators.amount(minAmount: 100.00);
        expect(validator('100.00'), isNull);
        expect(validator('99.99'), isNotNull);
      });

      test('respects maximum amount', () {
        final validator = InvoiceValidators.amount(maxAmount: 1000.00);
        expect(validator('1000.00'), isNull);
        expect(validator('1000.01'), isNotNull);
      });

      test('respects allowZero setting', () {
        final validator = InvoiceValidators.amount(allowZero: true);
        expect(validator('0.00'), isNull);
        expect(validator('-0.01'), isNotNull);
      });

      test('uses custom error message', () {
        final validator = InvoiceValidators.amount(
          errorMessage: 'Custom error',
        );
        expect(validator('abc'), 'Custom error');
      });
    });

    group('taxAmount', () {
      test('validates correct tax amounts', () {
        getInvoiceAmount() => 1000.00;
        final validator = InvoiceValidators.taxAmount(
          getInvoiceAmount,
          maxTaxRate: 0.25,
        );
        expect(validator('0.00'), isNull); // Zero allowed by default
        expect(validator('250.00'), isNull); // 25% tax
        expect(validator('100.00'), isNull); // 10% tax
      });

      test('rejects invalid tax amounts', () {
        getInvoiceAmount() => 1000.00;
        final validator = InvoiceValidators.taxAmount(
          getInvoiceAmount,
          maxTaxRate: 0.25,
        );
        expect(validator(''), isNull); // Let required validator handle this
        expect(validator('-100.00'), isNotNull); // Negative not allowed
        expect(validator('abc'), isNotNull); // Not a number
        expect(validator('251.00'), isNotNull); // Exceeds max tax rate
      });

      test('respects allowZero setting', () {
        getInvoiceAmount() => 1000.00;
        final validator = InvoiceValidators.taxAmount(
          getInvoiceAmount,
          maxTaxRate: 0.25,
          allowZero: false,
        );
        expect(validator('0.00'), isNotNull);
        expect(validator('0.01'), isNull);
      });

      test('handles zero invoice amount', () {
        getInvoiceAmount() => 0.00;
        final validator = InvoiceValidators.taxAmount(
          getInvoiceAmount,
          maxTaxRate: 0.25,
        );
        expect(
          validator('100.00'),
          isNull,
        ); // Can't validate against zero amount
      });

      test('uses custom error message', () {
        getInvoiceAmount() => 1000.00;
        final validator = InvoiceValidators.taxAmount(
          getInvoiceAmount,
          maxTaxRate: 0.25,
          errorMessage: 'Custom error',
        );
        expect(validator('251.00'), 'Custom error');
      });
    });

    group('vendorSelected', () {
      test('validates vendor selection', () {
        final validator = InvoiceValidators.vendorSelected();
        expect(validator(null), isNotNull);
        expect(validator(''), isNull);
        expect(validator(1), isNull);
        expect(validator({}), isNull);
      });

      test('uses custom error message', () {
        final validator = InvoiceValidators.vendorSelected('Custom error');
        expect(validator(null), 'Custom error');
      });
    });

    group('accountSelected', () {
      test('validates account selection', () {
        final validator = InvoiceValidators.accountSelected();
        expect(validator(null), isNotNull);
        expect(validator(''), isNull);
        expect(validator(1), isNull);
        expect(validator({}), isNull);
      });

      test('uses custom error message', () {
        final validator = InvoiceValidators.accountSelected('Custom error');
        expect(validator(null), 'Custom error');
      });
    });
  });
}
