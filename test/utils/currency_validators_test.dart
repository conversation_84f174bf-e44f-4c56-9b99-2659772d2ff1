import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/currency_validators.dart';

void main() {
  group('CurrencyValidators', () {
    group('currencyCode', () {
      test('validates correct currency codes', () {
        final validator = CurrencyValidators.currencyCode();
        expect(validator('USD'), isNull);
        expect(validator('EUR'), isNull);
        expect(validator('GBP'), isNull);
      });

      test('rejects invalid currency codes', () {
        final validator = CurrencyValidators.currencyCode();
        expect(validator('usd'), isNotNull); // Lowercase
        expect(validator('US'), isNotNull); // Too short
        expect(validator('USDD'), isNotNull); // Too long
        expect(validator('123'), isNotNull); // Numbers
        expect(validator('US\$'), isNotNull); // Special characters
      });

      test('handles null and empty values', () {
        final validator = CurrencyValidators.currencyCode();
        expect(validator(null), isNotNull); // Required
        expect(validator(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final validator = CurrencyValidators.currencyCode('Custom error');
        expect(validator(''), 'Custom error');
      });
    });

    group('currencyName', () {
      test('validates correct currency names', () {
        final validator = CurrencyValidators.currencyName();
        expect(validator('US Dollar'), isNull);
        expect(validator('Euro'), isNull);
        expect(validator('British Pound Sterling'), isNull);
      });

      test('rejects invalid currency names', () {
        final validator = CurrencyValidators.currencyName();
        expect(validator('A'), isNotNull); // Too short
        expect(validator('A' * 51), isNotNull); // Too long (default max 50)
      });

      test('handles custom max length', () {
        final validator = CurrencyValidators.currencyName(maxLength: 10);
        expect(validator('US Dollar'), isNull); // 9 chars
        expect(validator('Euro Dollar'), isNotNull); // 11 chars
      });

      test('handles null and empty values', () {
        final validator = CurrencyValidators.currencyName();
        expect(validator(null), isNotNull); // Required
        expect(validator(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final validator = CurrencyValidators.currencyName(
          errorMessage: 'Custom error',
        );
        expect(validator(''), 'Custom error');
      });
    });

    group('exchangeRate', () {
      test('validates correct exchange rates', () {
        final validator = CurrencyValidators.exchangeRate();
        expect(validator('1.00'), isNull);
        expect(validator('0.00001'), isNull); // Minimum default
        expect(validator('1000.00'), isNull);
      });

      test('rejects invalid exchange rates', () {
        final validator = CurrencyValidators.exchangeRate();
        expect(validator('0.000001'), isNotNull); // Below minimum
        expect(validator('0'), isNotNull); // Zero
        expect(validator('-1.00'), isNotNull); // Negative
        expect(validator('abc'), isNotNull); // Not a number
      });

      test('handles custom min and max rates', () {
        final validator = CurrencyValidators.exchangeRate(
          minRate: 0.1,
          maxRate: 10.0,
        );
        expect(validator('0.09'), isNotNull); // Below min
        expect(validator('0.10'), isNull); // At min
        expect(validator('5.00'), isNull); // In range
        expect(validator('10.00'), isNull); // At max
        expect(validator('10.01'), isNotNull); // Above max
      });

      test('handles null and empty values', () {
        final validator = CurrencyValidators.exchangeRate();
        expect(validator(null), isNotNull); // Required
        expect(validator(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final validator = CurrencyValidators.exchangeRate(
          errorMessage: 'Custom error',
        );
        expect(validator('abc'), 'Custom error');
      });
    });

    group('targetCurrencyNotSameAsBase', () {
      test('validates different currencies', () {
        getBaseCurrency() => 'USD';
        final validator = CurrencyValidators.targetCurrencyNotSameAsBase(
          getBaseCurrency,
        );
        expect(validator('EUR'), isNull);
        expect(validator('GBP'), isNull);
        expect(validator('USD'), isNotNull); // Same as base
      });

      test('handles null and empty values', () {
        getBaseCurrency() => 'USD';
        final validator = CurrencyValidators.targetCurrencyNotSameAsBase(
          getBaseCurrency,
        );
        expect(validator(null), isNull); // Let required validator handle this
        expect(validator(''), isNull); // Let required validator handle this
      });

      test('uses custom error message', () {
        getBaseCurrency() => 'USD';
        final validator = CurrencyValidators.targetCurrencyNotSameAsBase(
          getBaseCurrency,
          'Custom error',
        );
        expect(validator('USD'), 'Custom error');
      });
    });

    group('currencySymbol', () {
      test('validates correct currency symbols', () {
        final validator = CurrencyValidators.currencySymbol();
        expect(validator('\$'), isNull);
        expect(validator('€'), isNull);
        expect(validator('£'), isNull);
        expect(validator('¥'), isNull);
      });

      test('rejects invalid currency symbols', () {
        final validator = CurrencyValidators.currencySymbol();
        expect(validator(''), isNotNull); // Required
        expect(validator('\$' * 6), isNotNull); // Too long (default max 5)
      });

      test('handles custom max length', () {
        final validator = CurrencyValidators.currencySymbol(maxLength: 3);
        expect(validator('\$'), isNull); // 1 char
        expect(validator('USD'), isNull); // 3 chars
        expect(validator('USDD'), isNotNull); // 4 chars
      });

      test('handles null and empty values', () {
        final validator = CurrencyValidators.currencySymbol();
        expect(validator(null), isNotNull); // Required
        expect(validator(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final validator = CurrencyValidators.currencySymbol(
          errorMessage: 'Custom error',
        );
        expect(validator(''), 'Custom error');
      });
    });

    group('decimalPlaces', () {
      test('validates correct decimal places', () {
        final validator = CurrencyValidators.decimalPlaces();
        expect(validator('0'), isNull);
        expect(validator('2'), isNull);
        expect(validator('6'), isNull);
      });

      test('rejects invalid decimal places', () {
        final validator = CurrencyValidators.decimalPlaces();
        expect(validator('-1'), isNotNull); // Negative
        expect(validator('7'), isNotNull); // Too many
        expect(validator('abc'), isNotNull); // Not a number
      });

      test('handles null and empty values', () {
        final validator = CurrencyValidators.decimalPlaces();
        expect(validator(null), isNotNull); // Required
        expect(validator(''), isNotNull); // Required
      });

      test('uses custom error message', () {
        final validator = CurrencyValidators.decimalPlaces('Custom error');
        expect(validator('7'), 'Custom error');
      });
    });
  });
}
