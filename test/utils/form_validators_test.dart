import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/form_validators.dart';

void main() {
  group('FormValidators', () {
    group('required', () {
      test('returns error message for null value', () {
        final validator = FormValidators.required('Field is required');
        expect(validator(null), equals('Field is required'));
      });

      test('returns error message for empty string', () {
        final validator = FormValidators.required('Field is required');
        expect(validator(''), equals('Field is required'));
      });

      test('returns null for non-empty string', () {
        final validator = FormValidators.required('Field is required');
        expect(validator('test'), isNull);
      });
    });

    group('email', () {
      test('returns null for valid email addresses', () {
        final validator = FormValidators.email();
        expect(validator('<EMAIL>'), isNull);
        expect(validator('<EMAIL>'), isNull);
        expect(validator('<EMAIL>'), isNull);
      });

      test('returns error message for invalid email addresses', () {
        final validator = FormValidators.email();
        expect(validator('invalid-email'), isNotNull);
        expect(validator('@example.com'), isNotNull);
        expect(validator('test@'), isNotNull);
        expect(validator('test@.com'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.email();
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('minLength', () {
      test('returns null for strings meeting minimum length', () {
        final validator = FormValidators.minLength(3, 'Minimum 3 characters');
        expect(validator('abc'), isNull);
        expect(validator('abcd'), isNull);
      });

      test('returns error message for strings below minimum length', () {
        final validator = FormValidators.minLength(3, 'Minimum 3 characters');
        expect(validator('ab'), equals('Minimum 3 characters'));
        expect(validator('a'), equals('Minimum 3 characters'));
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.minLength(3, 'Minimum 3 characters');
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('maxLength', () {
      test('returns null for strings within maximum length', () {
        final validator = FormValidators.maxLength(3, 'Maximum 3 characters');
        expect(validator('abc'), isNull);
        expect(validator('ab'), isNull);
      });

      test('returns error message for strings exceeding maximum length', () {
        final validator = FormValidators.maxLength(3, 'Maximum 3 characters');
        expect(validator('abcd'), equals('Maximum 3 characters'));
        expect(validator('abcde'), equals('Maximum 3 characters'));
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.maxLength(3, 'Maximum 3 characters');
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('numeric', () {
      test('returns null for valid numeric values', () {
        final validator = FormValidators.numeric();
        expect(validator('123'), isNull);
        expect(validator('123.45'), isNull);
        expect(validator('-123'), isNull);
      });

      test('returns error message for non-numeric values', () {
        final validator = FormValidators.numeric();
        expect(validator('abc'), isNotNull);
        expect(validator('123abc'), isNotNull);
        expect(validator('abc123'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.numeric();
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('pattern', () {
      test('returns null for matching patterns', () {
        final validator = FormValidators.pattern(
          RegExp(r'^[A-Z]{2}\d{4}$'),
          'Must be 2 uppercase letters followed by 4 digits',
        );
        expect(validator('AB1234'), isNull);
        expect(validator('XY5678'), isNull);
      });

      test('returns error message for non-matching patterns', () {
        final validator = FormValidators.pattern(
          RegExp(r'^[A-Z]{2}\d{4}$'),
          'Must be 2 uppercase letters followed by 4 digits',
        );
        expect(validator('A1234'), isNotNull);
        expect(validator('AB123'), isNotNull);
        expect(validator('ab1234'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.pattern(
            RegExp(r'^[A-Z]{2}\d{4}$'),
            'Must be 2 uppercase letters followed by 4 digits',
          );
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('password', () {
      test('returns null for valid passwords with default requirements', () {
        final validator = FormValidators.password();
        expect(validator('Password123!'), isNull);
        expect(validator('SecureP@ss1'), isNull);
      });

      test('returns error message for passwords missing requirements', () {
        final validator = FormValidators.password();
        expect(
          validator('password'),
          isNotNull,
        ); // Missing uppercase, number, special char
        expect(
          validator('PASSWORD'),
          isNotNull,
        ); // Missing lowercase, number, special char
        expect(
          validator('Password'),
          isNotNull,
        ); // Missing number, special char
        expect(validator('Password123'), isNotNull); // Missing special char
      });

      test('respects custom password requirements', () {
        final validator = FormValidators.password(
          minLength: 10,
          requireUppercase: false,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
        );
        expect(validator('password123'), isNull);
        expect(validator('password'), isNotNull); // Too short
        expect(
          validator('password123!'),
          isNull,
        ); // Special char is allowed even when not required
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.password();
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('money', () {
      test('returns null for valid money values', () {
        final validator = FormValidators.money();
        expect(validator('123.45'), isNull);
        expect(validator('1,234.56'), isNull);
        expect(validator('\$123.45'), isNull);
      });

      test('respects minimum value constraint', () {
        final validator = FormValidators.money(minValue: 100);
        expect(validator('100'), isNull);
        expect(validator('100.01'), isNull);
        expect(validator('99.99'), isNotNull);
      });

      test('respects maximum value constraint', () {
        final validator = FormValidators.money(maxValue: 1000);
        expect(validator('1000'), isNull);
        expect(validator('999.99'), isNull);
        expect(validator('1000.01'), isNotNull);
      });

      test('respects negative value setting', () {
        final validator = FormValidators.money(allowNegative: true);
        expect(validator('-123.45'), isNull);

        final noNegativeValidator = FormValidators.money(allowNegative: false);
        expect(noNegativeValidator('-123.45'), isNotNull);
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.money();
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('matches', () {
      test('returns null for matching values', () {
        final validator = FormValidators.matches(
          () => 'password123',
          'Passwords do not match',
        );
        expect(validator('password123'), isNull);
      });

      test('returns error message for non-matching values', () {
        final validator = FormValidators.matches(
          () => 'password123',
          'Passwords do not match',
        );
        expect(validator('password456'), equals('Passwords do not match'));
      });

      test(
        'returns null for empty value (should be handled by required validator)',
        () {
          final validator = FormValidators.matches(
            () => 'password123',
            'Passwords do not match',
          );
          expect(validator(''), isNull);
          expect(validator(null), isNull);
        },
      );
    });

    group('combine', () {
      test('returns first error message from failing validators', () {
        final validator = FormValidators.combine([
          FormValidators.required('Required'),
          FormValidators.minLength(5, 'Min 5 chars'),
          FormValidators.email(),
        ]);

        expect(validator(null), equals('Required'));
        expect(validator(''), equals('Required'));
        expect(validator('a'), equals('Min 5 chars'));
        expect(
          validator('aaaaa'),
          equals('Please enter a valid email address'),
        );
        expect(validator('<EMAIL>'), isNull);
      });

      test('returns null when all validators pass', () {
        final validator = FormValidators.combine([
          FormValidators.required('Required'),
          FormValidators.email(),
          FormValidators.minLength(5, 'Min 5 chars'),
        ]);

        expect(validator('<EMAIL>'), isNull);
      });
    });
  });
}
