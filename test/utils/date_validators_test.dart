import 'package:flutter_test/flutter_test.dart';
import 'package:we_like_money/utils/date_validators.dart';

void main() {
  group('DateValidators', () {
    group('format', () {
      test('validates correct date format', () {
        final validator = DateValidators.format('yyyy-MM-dd');
        expect(validator('2024-03-15'), isNull);
        expect(validator('2024-12-31'), isNull);
        expect(validator('2024-01-01'), isNull);
      });

      test('rejects invalid date format', () {
        final validator = DateValidators.format('yyyy-MM-dd');
        expect(validator('not-a-date'), isNotNull);
        expect(validator('2024.03.15'), isNotNull);
        expect(validator('2024-13-45'), isNotNull);
        expect(validator('abc'), isNotNull);
      });

      test('handles null and empty values', () {
        final validator = DateValidators.format('yyyy-MM-dd');
        expect(validator(null), isNull);
        expect(validator(''), isNull);
      });
    });

    group('after', () {
      test('validates dates after minimum date', () {
        final minDate = DateTime(2024, 3, 1);
        final validator = DateValidators.after(minDate, 'yyyy-MM-dd');
        expect(validator('2024-03-15'), isNull);
        expect(validator('2024-04-01'), isNull);
        expect(validator('2024-02-28'), isNotNull);
      });

      test('handles inclusive minimum date', () {
        final minDate = DateTime(2024, 3, 1);
        final validator = DateValidators.after(
          minDate,
          'yyyy-MM-dd',
          null,
          true,
        );
        expect(validator('2024-03-01'), isNull);
        expect(validator('2024-02-29'), isNotNull);
      });

      test('handles null and empty values', () {
        final minDate = DateTime(2024, 3, 1);
        final validator = DateValidators.after(minDate, 'yyyy-MM-dd');
        expect(validator(null), isNull);
        expect(validator(''), isNull);
      });
    });

    group('before', () {
      test('validates dates before maximum date', () {
        final maxDate = DateTime(2024, 3, 31);
        final validator = DateValidators.before(maxDate, 'yyyy-MM-dd');
        expect(validator('2024-03-15'), isNull);
        expect(validator('2024-03-01'), isNull);
        expect(validator('2024-04-01'), isNotNull);
      });

      test('handles inclusive maximum date', () {
        final maxDate = DateTime(2024, 3, 31);
        final validator = DateValidators.before(
          maxDate,
          'yyyy-MM-dd',
          null,
          true,
        );
        expect(validator('2024-03-31'), isNull);
        expect(validator('2024-04-01'), isNotNull);
      });

      test('handles null and empty values', () {
        final maxDate = DateTime(2024, 3, 31);
        final validator = DateValidators.before(maxDate, 'yyyy-MM-dd');
        expect(validator(null), isNull);
        expect(validator(''), isNull);
      });
    });

    group('between', () {
      test('validates dates between minimum and maximum dates', () {
        final minDate = DateTime(2024, 3, 1);
        final maxDate = DateTime(2024, 3, 31);
        final validator = DateValidators.between(
          minDate,
          maxDate,
          'yyyy-MM-dd',
        );
        expect(validator('2024-03-15'), isNull);
        expect(validator('2024-02-28'), isNotNull);
        expect(validator('2024-04-01'), isNotNull);
      });

      test('handles inclusive dates', () {
        final minDate = DateTime(2024, 3, 1);
        final maxDate = DateTime(2024, 3, 31);
        final validator = DateValidators.between(
          minDate,
          maxDate,
          'yyyy-MM-dd',
          null,
          true,
          true,
        );
        expect(validator('2024-03-01'), isNull);
        expect(validator('2024-03-31'), isNull);
        expect(validator('2024-02-29'), isNotNull);
        expect(validator('2024-04-01'), isNotNull);
      });

      test('handles null and empty values', () {
        final minDate = DateTime(2024, 3, 1);
        final maxDate = DateTime(2024, 3, 31);
        final validator = DateValidators.between(
          minDate,
          maxDate,
          'yyyy-MM-dd',
        );
        expect(validator(null), isNull);
        expect(validator(''), isNull);
      });
    });

    group('notWeekend', () {
      test('validates non-weekend dates', () {
        final validator = DateValidators.notWeekend('yyyy-MM-dd');
        expect(validator('2024-03-15'), isNull); // Friday
        expect(validator('2024-03-18'), isNull); // Monday
        expect(validator('2024-03-16'), isNotNull); // Saturday
        expect(validator('2024-03-17'), isNotNull); // Sunday
      });

      test('handles null and empty values', () {
        final validator = DateValidators.notWeekend('yyyy-MM-dd');
        expect(validator(null), isNull);
        expect(validator(''), isNull);
      });
    });

    group('businessDay', () {
      test('validates business days', () {
        final holidays = [
          DateTime(2024, 3, 15), // Friday
          DateTime(2024, 3, 18), // Monday
        ];
        final validator = DateValidators.businessDay(
          'yyyy-MM-dd',
          holidays: holidays,
        );
        expect(validator('2024-03-14'), isNull); // Thursday
        expect(validator('2024-03-15'), isNotNull); // Holiday
        expect(validator('2024-03-16'), isNotNull); // Saturday
        expect(validator('2024-03-17'), isNotNull); // Sunday
        expect(validator('2024-03-18'), isNotNull); // Holiday
        expect(validator('2024-03-19'), isNull); // Tuesday
      });

      test('handles null and empty values', () {
        final validator = DateValidators.businessDay('yyyy-MM-dd');
        expect(validator(null), isNull);
        expect(validator(''), isNull);
      });
    });
  });
}
