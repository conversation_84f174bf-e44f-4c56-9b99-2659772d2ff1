import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A filter bar for the general ledger screen
class LedgerFilterBar extends StatelessWidget {
  /// The current search query
  final String searchQuery;

  /// The selected account number
  final String? selectedAccountNumber;

  /// The start date for filtering
  final DateTime? startDate;

  /// The end date for filtering
  final DateTime? endDate;

  /// Callback when the search query changes
  final ValueChanged<String> onSearchChanged;

  /// Callback when the account number changes
  final ValueChanged<String?> onAccountNumberChanged;

  /// Callback when the start date changes
  final ValueChanged<DateTime?> onStartDateChanged;

  /// Callback when the end date changes
  final ValueChanged<DateTime?> onEndDateChanged;

  /// Callback when the clear filters button is pressed
  final VoidCallback onClearFilters;

  /// List of available account numbers
  final List<String> accountNumbers;

  /// Date format for displaying dates
  final DateFormat dateFormat;

  /// Constructor
  const LedgerFilterBar({
    required this.searchQuery,
    required this.selectedAccountNumber,
    required this.startDate,
    required this.endDate,
    required this.onSearchChanged,
    required this.onAccountNumberChanged,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    required this.onClearFilters,
    required this.accountNumbers,
    required this.dateFormat,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Filters', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),

            // Search field
            TextField(
              controller: TextEditingController(text: searchQuery)
                ..selection = TextSelection.fromPosition(
                  TextPosition(offset: searchQuery.length),
                ),
              decoration: InputDecoration(
                labelText: 'Search',
                hintText: 'Search by description',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
                suffixIcon:
                    searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => onSearchChanged(''),
                        )
                        : null,
              ),
              onChanged: onSearchChanged,
            ),
            const SizedBox(height: 16),

            // Account dropdown
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                labelText: 'Account',
                border: OutlineInputBorder(),
              ),
              value: selectedAccountNumber,
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('All Accounts'),
                ),
                ...accountNumbers.map(
                  (accountNumber) => DropdownMenuItem<String?>(
                    value: accountNumber,
                    child: Text(accountNumber),
                  ),
                ),
              ],
              onChanged: onAccountNumberChanged,
            ),
            const SizedBox(height: 16),

            // Date range
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'From Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        startDate != null
                            ? dateFormat.format(startDate!)
                            : 'Select Date',
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'To Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        endDate != null
                            ? dateFormat.format(endDate!)
                            : 'Select Date',
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Clear filters button
            Center(
              child: ElevatedButton.icon(
                onPressed: onClearFilters,
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear Filters'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show date picker for selecting start date
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: startDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      onStartDateChanged(picked);
    }
  }

  /// Show date picker for selecting end date
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: endDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      onEndDateChanged(picked);
    }
  }
}
