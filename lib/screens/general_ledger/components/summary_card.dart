import 'package:flutter/material.dart';

/// A card that displays summary information for the general ledger
class LedgerSummaryCard extends StatelessWidget {
  /// The total debits
  final double totalDebits;
  
  /// The total credits
  final double totalCredits;
  
  /// The net balance
  final double netBalance;
  
  /// Constructor
  const LedgerSummaryCard({
    required this.totalDebits,
    required this.totalCredits,
    required this.netBalance,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Summary',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildSummaryItem(
                  context,
                  'Total Debits',
                  totalDebits,
                  Colors.red,
                ),
                _buildSummaryItem(
                  context,
                  'Total Credits',
                  totalCredits,
                  Colors.green,
                ),
                _buildSummaryItem(
                  context,
                  'Net Balance',
                  netBalance,
                  netBalance >= 0 ? Colors.green : Colors.red,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build a summary item with label and value
  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    double value,
    Color valueColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 4),
        Text(
          '\$${value.abs().toStringAsFixed(2)}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: valueColor,
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }
}
