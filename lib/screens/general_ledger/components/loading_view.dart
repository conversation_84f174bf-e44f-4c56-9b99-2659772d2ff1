import 'package:flutter/material.dart';

/// A widget that displays a loading indicator
class LedgerLoadingView extends StatelessWidget {
  /// Constructor
  const LedgerLoadingView({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          Sized<PERSON>ox(height: 16),
          Text('Loading ledger entries...'),
        ],
      ),
    );
  }
}
