import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/providers/general_ledger_provider.dart';
import 'package:we_like_money/providers/vendor_invoice_provider.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/widgets/error_display.dart';
import 'package:we_like_money/widgets/loading_display.dart';
import 'package:we_like_money/screens/vendor_invoice/vendor_invoice_entry_screen.dart';

/// A screen that displays the details of a vendor invoice
class VendorInvoiceDetailScreen extends ConsumerWidget {
  /// The ID of the invoice to display
  final int invoiceId;

  /// Date format for display
  late final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  /// Currency format for display
  late final NumberFormat _currencyFormat = NumberFormat.currency(symbol: '\$');

  /// Creates a vendor invoice detail screen
  VendorInvoiceDetailScreen({required this.invoiceId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the invoice data
    final invoiceAsync = ref.watch(vendorInvoiceByIdProvider(invoiceId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Invoice Details'),
        actions: [
          // Edit invoice
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit Invoice',
            onPressed: () {
              // Navigate to the edit screen with the current invoice
              invoiceAsync.whenData((invoice) {
                if (invoice != null) {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              VendorInvoiceEntryScreen(vendorInvoice: invoice),
                    ),
                  );
                }
              });
            },
          ),
        ],
      ),
      body: invoiceAsync.when(
        loading:
            () => const LoadingDisplay(message: 'Loading invoice details...'),
        error:
            (error, _) => ErrorDisplay(
              errorMessage: 'Error loading invoice: $error',
              onRetry: () => ref.refresh(vendorInvoiceByIdProvider(invoiceId)),
            ),
        data: (invoice) {
          if (invoice == null) {
            return const Center(child: Text('Invoice not found'));
          }

          // Watch the vendor associated with this invoice
          final vendorAsync = ref.watch(vendorByIdProvider(invoice.vendorId));

          // Watch general ledger entries for this invoice
          final ledgerEntriesAsync = ref.watch(
            generalLedgerEntriesByDescriptionProvider(
              'Invoice #${invoice.invoiceNumber}',
            ),
          );

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Invoice header card
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Invoice number
                            Text(
                              'Invoice #${invoice.invoiceNumber}',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),

                            // Payment status
                            Chip(
                              backgroundColor:
                                  (invoice.isPaid ?? false)
                                      ? Colors.green
                                      : Colors.orange,
                              label: Text(
                                (invoice.isPaid ?? false) ? 'Paid' : 'Unpaid',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                        const Divider(),

                        // Vendor information
                        vendorAsync.when(
                          loading: () => const Text('Loading vendor...'),
                          error:
                              (_, __) => const Text(
                                'Error loading vendor information',
                              ),
                          data: (vendor) {
                            if (vendor == null) {
                              return const Text('Unknown vendor');
                            }

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Vendor',
                                  style: Theme.of(context).textTheme.titleSmall,
                                ),
                                Text(
                                  vendor.vendorName,
                                  style: Theme.of(context).textTheme.bodyLarge,
                                ),
                                if (vendor.organizationNumber != null)
                                  Text(
                                    'Org. Number: ${vendor.organizationNumber}',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                if (vendor.email != null)
                                  Text(
                                    'Email: ${vendor.email}',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Invoice details card
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Invoice Details',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),

                        // Amount
                        _buildDetailRow(
                          context,
                          'Amount:',
                          _currencyFormat.format(invoice.amount),
                        ),

                        // Tax amount
                        if (invoice.taxAmount != null)
                          _buildDetailRow(
                            context,
                            'Tax Amount:',
                            _currencyFormat.format(invoice.taxAmount!),
                          ),

                        // Total amount
                        _buildDetailRow(
                          context,
                          'Total Amount:',
                          _currencyFormat.format(
                            invoice.amount + (invoice.taxAmount ?? 0),
                          ),
                          isBold: true,
                        ),

                        const Divider(),

                        // Dates
                        _buildDetailRow(
                          context,
                          'Invoice Date:',
                          _dateFormat.format(invoice.invoiceDate),
                        ),

                        _buildDetailRow(
                          context,
                          'Due Date:',
                          _dateFormat.format(invoice.dueDate),
                        ),

                        const Divider(),

                        // Account information
                        _buildDetailRow(
                          context,
                          'Expense Account:',
                          invoice.expenseAccountNumber,
                        ),

                        _buildDetailRow(
                          context,
                          'Currency:',
                          invoice.currencyCode,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // General ledger entries
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'General Ledger Entries',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),

                        ledgerEntriesAsync.when(
                          loading:
                              () => const Center(
                                child: CircularProgressIndicator(),
                              ),
                          error:
                              (error, _) =>
                                  Text('Error loading ledger entries: $error'),
                          data: (entries) {
                            if (entries.isEmpty) {
                              return const Text(
                                'No general ledger entries found for this invoice.',
                              );
                            }

                            return Column(
                              children: [
                                const ListTile(
                                  title: Row(
                                    children: [
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'Account',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          'Debit',
                                          textAlign: TextAlign.end,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          'Credit',
                                          textAlign: TextAlign.end,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const Divider(),
                                ...entries.map(
                                  (entry) => _buildLedgerEntryRow(entry),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Builds a detail row with a label and value
  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(label, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style:
                  isBold
                      ? const TextStyle(fontWeight: FontWeight.bold)
                      : Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a row for a general ledger entry
  Widget _buildLedgerEntryRow(GeneralLedger entry) {
    return ListTile(
      title: Row(
        children: [
          Expanded(flex: 2, child: Text(entry.accountNumber)),
          Expanded(
            child: Text(
              entry.debit > 0 ? _currencyFormat.format(entry.debit) : '',
              textAlign: TextAlign.end,
            ),
          ),
          Expanded(
            child: Text(
              entry.credit > 0 ? _currencyFormat.format(entry.credit) : '',
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
      subtitle: Text(
        _dateFormat.format(entry.transactionDate),
        style: const TextStyle(fontSize: 12),
      ),
    );
  }
}
