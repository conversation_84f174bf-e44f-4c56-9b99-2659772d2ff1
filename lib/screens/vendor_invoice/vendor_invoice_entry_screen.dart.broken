import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/models/staff_member.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/providers/account_provider.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/providers/staff_member_provider.dart';
import 'package:we_like_money/providers/vendor_invoice_provider.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/screens/error_display.dart';
import 'package:we_like_money/screens/loading_display.dart';
import 'package:we_like_money/screens/vendor/vendor_quick_create_dialog.dart';
import 'package:we_like_money/utils/debouncer.dart';
import 'package:we_like_money/utils/extensions.dart';
import 'package:we_like_money/utils/form_validators.dart';

/// Screen for creating a new vendor invoice
class VendorInvoiceEntryScreen extends HookConsumerWidget {
  /// Constructor
  const VendorInvoiceEntryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Form key
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Form field controllers
    final vendorSearchController = useTextEditingController();
    final invoiceNumberController = useTextEditingController();
    final amountController = useTextEditingController();
    final taxAmountController = useTextEditingController();

    // Selected items
    final selectedVendor = useState<Vendor?>(null);
    final selectedAccount = useState<Account?>(null);
    final selectedProject = useState<Project?>(null);
    final selectedStaffMember = useState<StaffMember?>(null);

    // Dates
    final invoiceDate = useState(DateTime.now());
    final dueDate = useState(DateTime.now().add(const Duration(days: 30)));

    // Currency
    final currency = useState('USD');

    // Loading state
    final isLoading = useState(false);

    // Debouncer for vendor search
    final debouncer = useMemoized(() => Debouncer(milliseconds: 500));

    // Search results
    final vendorSearchQuery = useState('');

    // Watch vendor search results
    final vendorSearchResult = ref.watch(
      vendorSearchProvider(vendorSearchQuery.value),
    );

    // Watch accounts, projects and staff members
    final accountsAsyncValue = ref.watch(accountsProvider);
    final projectsAsyncValue = ref.watch(projectsProvider);
    final staffMembersAsyncValue = ref.watch(staffMembersProvider);

    // Create function
    final createVendorInvoice = ref.watch(vendorInvoiceCreationProvider);

    // Search for vendors
    void searchVendors(String query) {
      if (query.isNotEmpty) {
        debouncer.run(() {
          vendorSearchQuery.value = query;
        });
      }
    }

    // Handle vendor selection
    void selectVendor(Vendor vendor) {
      selectedVendor.value = vendor;
      vendorSearchController.text = vendor.vendorName;
    }

    // Show vendor quick create dialog
    Future<void> showVendorCreateDialog() async {
      final vendor = await showDialog<Vendor>(
        context: context,
        builder: (context) => const VendorQuickCreateDialog(),
      );

      if (vendor != null) {
        selectVendor(vendor);
      }
    }

    // Save the invoice
    Future<void> saveInvoice() async {
      if (formKey.currentState?.validate() != true) {
        return;
      }

      if (selectedVendor.value == null) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Please select a vendor')));
        return;
      }

      if (selectedAccount.value == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select an expense account')),
        );
        return;
      }

      try {
        isLoading.value = true;

        final vendorInvoice = VendorInvoice(
          invoiceId: 0, // This will be assigned by the database
          vendorId: selectedVendor.value!.vendorId,
          invoiceNumber: invoiceNumberController.text,
          invoiceDate: invoiceDate.value,
          dueDate: dueDate.value,
          amount: double.parse(amountController.text),
          currencyCode: currency.value,
          expenseAccountNumber: selectedAccount.value!.accountNumber,
          taxAmount:
              taxAmountController.text.isNotEmpty
                  ? double.parse(taxAmountController.text)
                  : null,
          projectId: selectedProject.value?.projectId,
          staffId: selectedStaffMember.value?.staffId,
          isPaid: false,
        );

        await createVendorInvoice(vendorInvoice);

        // Show success and navigate back
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Invoice created successfully')),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Error creating invoice: $e')));
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(title: const Text('New Vendor Invoice')),
      body:
          isLoading.value
              ? const LoadingDisplay(message: 'Creating invoice...')
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Vendor selection
                        const Text(
                          'Vendor',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: vendorSearchController,
                                decoration: InputDecoration(
                                  labelText: 'Search for vendor',
                                  border: const OutlineInputBorder(),
                                  suffixIcon:
                                      selectedVendor.value != null
                                          ? IconButton(
                                            icon: const Icon(Icons.clear),
                                            onPressed: () {
                                              selectedVendor.value = null;
                                              vendorSearchController.clear();
                                            },
                                          )
                                          : null,
                                ),
                                onChanged: searchVendors,
                                validator: FormValidators.required(
                                  'Vendor is required',
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: showVendorCreateDialog,
                              child: const Text('New'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // Vendor search results
                        vendorSearchResult.when(
                          data: (vendors) {
                            if (vendors.isEmpty ||
                                selectedVendor.value != null) {
                              return const SizedBox.shrink();
                            }

                            return SizedBox(
                              height: 150,
                              child: Card(
                                child: ListView.builder(
                                  itemCount: vendors.length,
                                  itemBuilder: (context, index) {
                                    final vendor = vendors[index];
                                    return ListTile(
                                      title: Text(vendor.vendorName),
                                      subtitle: Text(vendor.address ?? ''),
                                      onTap: () => selectVendor(vendor),
                                    );
                                  },
                                ),
                              ),
                            );
                          },
                          loading:
                              () => const SizedBox(
                                height: 50,
                                child: Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                          error:
                              (error, stackTrace) => SizedBox(
                                height: 50,
                                child: ErrorDisplay(
                                  message: 'Error loading vendors: $error',
                                  onRetry:
                                      () => searchVendors(
                                        vendorSearchController.text,
                                      ),
                                ),
                              ),
                        ),

                        const SizedBox(height: 16),

                        // Invoice details
                        const Text(
                          'Invoice Details',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),

                        // Invoice number
                        TextFormField(
                          controller: invoiceNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Invoice Number',
                            border: OutlineInputBorder(),
                          ),
                          validator: FormValidators.required(
                            'Invoice number is required',
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Invoice date and due date
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: invoiceDate.value,
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2100),
                                  );
                                  if (date != null) {
                                    invoiceDate.value = date;
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Invoice Date',
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(invoiceDate.value.toLocalDate()),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: dueDate.value,
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2100),
                                  );
                                  if (date != null) {
                                    dueDate.value = date;
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Due Date',
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(dueDate.value.toLocalDate()),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Amount and currency
                        Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: TextFormField(
                                controller: amountController,
                                decoration: const InputDecoration(
                                  labelText: 'Amount',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.number,
                                validator: FormValidators.required(
                                  'Amount is required',
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: DropdownButtonFormField<String>(
                                decoration: const InputDecoration(
                                  labelText: 'Currency',
                                  border: OutlineInputBorder(),
                                ),
                                value: currency.value,
                                items: const [
                                  DropdownMenuItem(
                                    value: 'USD',
                                    child: Text('USD'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'EUR',
                                    child: Text('EUR'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'GBP',
                                    child: Text('GBP'),
                                  ),
                                ],
                                onChanged: (value) {
                                  if (value != null) {
                                    currency.value = value;
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Tax amount
                        TextFormField(
                          controller: taxAmountController,
                          decoration: const InputDecoration(
                            labelText: 'Tax Amount (Optional)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 16),

                        // Expense account
                        const Text(
                          'Accounting',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),

                        accountsAsyncValue.when(
                          data: (accounts) {
                            final expenseAccounts =
                                accounts
                                    .where(
                                      (account) =>
                                          account.accountNumber.startsWith('5'),
                                    )
                                    .toList();

                            return DropdownButtonFormField<Account>(
                              decoration: const InputDecoration(
                                labelText: 'Expense Account',
                                border: OutlineInputBorder(),
                              ),
                              hint: const Text('Select expense account'),
                              value: selectedAccount.value,
                              items:
                                  expenseAccounts.map((account) {
                                    return DropdownMenuItem(
                                      value: account,
                                      child: Text(
                                        '${account.accountNumber} - ${account.accountName}',
                                      ),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  selectedAccount.value = value;
                                }
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Expense account is required';
                                }
                                return null;
                              },
                            );
                          },
                          loading:
                              () => const Center(
                                child: CircularProgressIndicator(),
                              ),
                          error:
                              (error, stackTrace) => ErrorDisplay(
                                message: 'Error loading accounts: $error',
                                onRetry: () => ref.refresh(accountsProvider),
                              ),
                        ),
                        const SizedBox(height: 16),

                        // Optional fields
                        const Text(
                          'Optional Fields',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),

                        // Project dropdown
                        projectsAsyncValue.when(
                          data: (projects) {
                            return DropdownButtonFormField<Project?>(
                              decoration: const InputDecoration(
                                labelText: 'Project (Optional)',
                                border: OutlineInputBorder(),
                              ),
                              hint: const Text('Select project'),
                              value: selectedProject.value,
                              items: [
                                const DropdownMenuItem(
                                  value: null,
                                  child: Text('None'),
                                ),
                                ...projects.map((project) {
                                  return DropdownMenuItem(
                                    value: project,
                                    child: Text(
                                      '${project.projectCode} - ${project.projectName}',
                                    ),
                                  );
                                }).toList(),
                              ],
                              onChanged: (value) {
                                selectedProject.value = value;
                              },
                            );
                          },
                          loading:
                              () => const Center(
                                child: CircularProgressIndicator(),
                              ),
                          error:
                              (error, stackTrace) => ErrorDisplay(
                                message: 'Error loading projects: $error',
                                onRetry: () => ref.refresh(projectsProvider),
                              ),
                        ),
                        const SizedBox(height: 16),

                        // Staff member dropdown
                        staffMembersAsyncValue.when(
                          data: (staffMembers) {
                            return DropdownButtonFormField<StaffMember?>(
                              decoration: const InputDecoration(
                                labelText: 'Staff Member (Optional)',
                                border: OutlineInputBorder(),
                              ),
                              hint: const Text('Select staff member'),
                              value: selectedStaffMember.value,
                              items: [
                                const DropdownMenuItem(
                                  value: null,
                                  child: Text('None'),
                                ),
                                ...staffMembers.map((staffMember) {
                                  return DropdownMenuItem(
                                    value: staffMember,
                                    child: Text(staffMember.staffName),
                                  );
                                }).toList(),
                              ],
                              onChanged: (value) {
                                selectedStaffMember.value = value;
                              },
                            );
                          },
                          loading:
                              () => const Center(
                                child: CircularProgressIndicator(),
                              ),
                          error:
                              (error, stackTrace) => ErrorDisplay(
                                message: 'Error loading staff members: $error',
                                onRetry:
                                    () => ref.refresh(staffMembersProvider),
                              ),
                        ),
                        const SizedBox(height: 32),

                        // Save button
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: saveInvoice,
                            child: const Text('Create Invoice'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }
}
