import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/providers/account_provider.dart';
import 'package:we_like_money/screens/vendor_invoice/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/fields/dropdown_form_field.dart';
import 'package:we_like_money/ui/forms/form_section.dart';
import 'package:we_like_money/widgets/error_display.dart';

/// A refactored form section for accounting details
class AccountingSectionRefactored extends ConsumerWidget {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// Constructor
  const AccountingSectionRefactored({required this.viewModel, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _AccountingSectionContent(viewModel: viewModel, ref: ref);
  }
}

/// Content widget for the accounting section
class _AccountingSectionContent extends FormSection {
  /// The view model
  final VendorInvoiceViewModel viewModel;

  /// The provider reference
  final WidgetRef ref;

  /// Constructor
  const _AccountingSectionContent({required this.viewModel, required this.ref})
    : super(
        title: 'Accounting',
        helpText: 'Select the expense account for this invoice',
        headerIcon: Icons.account_balance,
      );

  @override
  Widget buildSectionContent(BuildContext context) {
    // Watch accounts
    final accountsAsyncValue = ref.watch(accountsProvider);

    return accountsAsyncValue.when(
      data: (accounts) {
        // Filter accounts to only expense accounts (5000-5999)
        final expenseAccounts =
            accounts.where((account) {
              final accountNum = int.tryParse(
                account.accountNumber.replaceAll(RegExp(r'[^0-9]'), ''),
              );
              return accountNum != null &&
                  accountNum >= 5000 &&
                  accountNum < 6000;
            }).toList();

        return _buildAccountDropdown(context, expenseAccounts);
      },
      loading:
          () => const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16.0),
              child: CircularProgressIndicator(),
            ),
          ),
      error:
          (error, stackTrace) => ErrorDisplay(
            errorMessage: 'Error loading accounts: $error',
            onRetry: () => ref.refresh(accountsProvider),
          ),
    );
  }

  /// Build the account dropdown
  Widget _buildAccountDropdown(BuildContext context, List<Account> accounts) {
    return ValueListenableBuilder(
      valueListenable: viewModel.selectedAccount,
      builder: (context, selectedAccount, _) {
        return AppDropdownFormField<Account>.required(
          value: selectedAccount,
          onChanged: (account) {
            if (account != null) {
              viewModel.selectedAccount.value = account;
            }
          },
          items: accounts,
          displayStringBuilder:
              (account) => '${account.accountNumber} - ${account.accountName}',
          labelText: 'Expense Account',
          prefixIcon: Icons.account_balance,
          errorMessage: 'Please select an expense account',
        );
      },
    );
  }
}
