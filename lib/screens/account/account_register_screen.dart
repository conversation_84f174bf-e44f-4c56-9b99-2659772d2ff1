import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';
import 'package:we_like_money/utils/exceptions.dart';

/// A screen that displays the transaction register for a specific account.
class AccountRegisterScreen extends StatefulWidget {
  final Account account;

  const AccountRegisterScreen({super.key, required this.account});

  @override
  State<AccountRegisterScreen> createState() => _AccountRegisterScreenState();
}

class _AccountRegisterScreenState extends State<AccountRegisterScreen> {
  final AccountViewModel _viewModel = GetIt.instance<AccountViewModel>();
  bool _isLoading = true;
  List<GeneralLedger> _entries = [];
  double _balance = 0.0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAccountData();
  }

  Future<void> _loadAccountData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // First try to get the account
      final account = await _viewModel.getAccountByNumber(
        widget.account.accountNumber,
      );

      if (account == null) {
        throw BusinessException('Unable to fetch account');
      }

      final entries = await _viewModel.getGeneralLedgerEntriesByAccount(
        widget.account.accountNumber,
      );
      final balance = await _viewModel.calculateAccountBalance(
        widget.account.accountNumber,
      );

      setState(() {
        _entries = entries;
        _balance = balance;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage =
            e is BusinessException
                ? 'Error: BusinessException: ${e.message}'
                : 'Error: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Account: ${widget.account.accountName}')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAccountData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        _buildAccountSummary(),
        Expanded(
          child:
              _entries.isEmpty
                  ? const Center(
                    child: Text('No transactions found for this account.'),
                  )
                  : _buildTransactionList(),
        ),
      ],
    );
  }

  Widget _buildAccountSummary() {
    final isPositive = _balance >= 0;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account #: ${widget.account.accountNumber}',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Current Balance:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Text(
                '\$${_balance.abs().toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: isPositive ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList() {
    // Sort entries by transaction date (newest first)
    final sortedEntries = List<GeneralLedger>.from(_entries)
      ..sort((a, b) => b.transactionDate.compareTo(a.transactionDate));

    return ListView.separated(
      itemCount: sortedEntries.length,
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final entry = sortedEntries[index];
        return _buildTransactionItem(entry);
      },
    );
  }

  Widget _buildTransactionItem(GeneralLedger entry) {
    final dateFormat = DateFormat('MMM dd, yyyy');
    final formattedDate = dateFormat.format(entry.transactionDate);

    // Determine if this is a debit or credit entry
    final isDebit = entry.debit > 0;
    final amount = isDebit ? entry.debit : entry.credit;
    final transactionType = isDebit ? 'Deposit' : 'Withdrawal';

    return ListTile(
      title: Text('$transactionType - ${entry.description}'),
      subtitle: Text(formattedDate),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            alignment: Alignment.centerRight,
            child: Text(
              isDebit ? 'Dr: \$${amount.toStringAsFixed(2)}' : '',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          Container(
            width: 80,
            alignment: Alignment.centerRight,
            child: Text(
              !isDebit ? 'Cr: \$${amount.toStringAsFixed(2)}' : '',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
      onTap: () {
        // TODO: Show detailed transaction view if needed
      },
    );
  }
}
