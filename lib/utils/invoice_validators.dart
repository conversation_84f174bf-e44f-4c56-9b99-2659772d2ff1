import 'package:intl/intl.dart';
import 'form_validators.dart';

/// A collection of validators specific to invoice functionality
class InvoiceValidators {
  /// Creates a validator for invoice numbers
  ///
  /// Ensures the invoice number follows the required format
  /// Default pattern allows alphanumeric characters and common separators
  static String? Function(String?) invoiceNumber({
    RegExp? pattern,
    String? errorMessage,
    int maxLength = 50,
  }) {
    final invoicePattern = pattern ?? RegExp(r'^[a-zA-Z0-9\-_/\.]+$');

    return FormValidators.combine([
      FormValidators.required(errorMessage ?? 'Invoice number is required'),
      FormValidators.maxLength(
        maxLength,
        'Invoice number is too long (maximum $maxLength characters)',
      ),
      FormValidators.pattern(
        invoicePattern,
        errorMessage ?? 'Invalid invoice number format',
      ),
    ]);
  }

  /// Creates a validator for due dates relative to invoice dates
  ///
  /// [invoiceDate] is the date the invoice was issued
  /// [minDays] is the minimum number of days allowed between invoice and due date
  /// [maxDays] is the maximum number of days allowed between invoice and due date
  /// [dateFormat] is the format used to parse the input (e.g., 'yyyy-MM-dd')
  static String? Function(String?) dueDateValidator(
    DateTime invoiceDate, {
    int minDays = 0,
    int? maxDays,
    String dateFormat = 'yyyy-MM-dd',
    String? errorMessage,
  }) {
    final formatter = DateFormat(dateFormat);
    // Normalize invoice date to start of day
    final normalizedInvoiceDate = DateTime(
      invoiceDate.year,
      invoiceDate.month,
      invoiceDate.day,
    );

    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final dueDate = formatter.parseStrict(value);
        // Normalize due date to start of day
        final normalizedDueDate = DateTime(
          dueDate.year,
          dueDate.month,
          dueDate.day,
        );

        final difference =
            normalizedDueDate.difference(normalizedInvoiceDate).inDays;

        if (difference < minDays) {
          return errorMessage ??
              'Due date must be at least $minDays days after invoice date';
        }

        if (maxDays != null && difference > maxDays) {
          return errorMessage ??
              'Due date cannot be more than $maxDays days after invoice date';
        }

        return null;
      } catch (e) {
        // Format error should be caught by a format validator
        return null;
      }
    };
  }

  /// Creates a validator for invoice amounts
  ///
  /// Ensures the amount is within acceptable limits
  /// [minAmount] is the minimum allowed amount (default: 0.01)
  /// [maxAmount] is the optional maximum allowed amount
  /// [allowZero] determines if zero amounts are acceptable (default: false)
  static String? Function(String?) amount({
    double minAmount = 0.01,
    double? maxAmount,
    bool allowZero = false,
    String? errorMessage,
  }) {
    final actualMinAmount = allowZero ? 0.0 : minAmount;

    return FormValidators.combine([
      FormValidators.required('Amount is required'),
      FormValidators.money(
        minValue: actualMinAmount,
        maxValue: maxAmount,
        allowNegative: false,
        errorMessage: errorMessage,
      ),
    ]);
  }

  /// Creates a validator for tax amounts
  ///
  /// Ensures the tax amount is appropriate relative to the invoice amount
  /// [getInvoiceAmount] function that returns the current invoice amount
  /// [maxTaxRate] maximum allowed tax rate as decimal (e.g., 0.25 for 25%)
  static String? Function(String?) taxAmount(
    double Function() getInvoiceAmount, {
    double maxTaxRate = 0.25,
    bool allowZero = true,
    String? errorMessage,
  }) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      // Clean the input and try to parse it
      final cleanValue = value.replaceAll(RegExp(r'[$,]'), '');
      final parsedValue = double.tryParse(cleanValue);

      if (parsedValue == null) {
        return 'Please enter a valid tax amount';
      }

      // Check if zero is allowed
      if (!allowZero && parsedValue == 0) {
        return 'Tax amount cannot be zero';
      }

      // Check if negative
      if (parsedValue < 0) {
        return 'Tax amount cannot be negative';
      }

      // Check against invoice amount
      final invoiceAmount = getInvoiceAmount();
      if (invoiceAmount <= 0) {
        return null; // Can't validate against invalid invoice amount
      }

      // Calculate the implied tax rate
      final impliedTaxRate = parsedValue / invoiceAmount;

      // Check if tax rate is reasonable
      if (impliedTaxRate > maxTaxRate) {
        return errorMessage ??
            'Tax amount exceeds ${(maxTaxRate * 100).toStringAsFixed(0)}% of invoice amount';
      }

      return null;
    };
  }

  /// Creates a validator for vendor selection
  ///
  /// Ensures a vendor has been selected
  static String? Function(dynamic) vendorSelected([
    String errorMessage = 'Please select a vendor',
  ]) {
    return (dynamic value) {
      if (value == null) {
        return errorMessage;
      }
      return null;
    };
  }

  /// Creates a validator for expense account selection
  ///
  /// Ensures an expense account has been selected
  static String? Function(dynamic) accountSelected([
    String errorMessage = 'Please select an expense account',
  ]) {
    return (dynamic value) {
      if (value == null) {
        return errorMessage;
      }
      return null;
    };
  }
}
