import 'form_validators.dart';

/// A collection of validators specific to currency functionality
class CurrencyValidators {
  /// Creates a validator for currency codes
  ///
  /// Ensures the currency code follows the ISO 4217 standard (3 uppercase letters)
  static String? Function(String?) currencyCode([String? errorMessage]) {
    final currencyCodePattern = RegExp(r'^[A-Z]{3}$');

    return FormValidators.combine([
      FormValidators.required(errorMessage ?? 'Currency code is required'),
      FormValidators.pattern(
        currencyCodePattern,
        errorMessage ?? 'Currency code must be 3 uppercase letters (ISO 4217)',
      ),
    ]);
  }

  /// Creates a validator for currency names
  ///
  /// Ensures the currency name is valid
  /// [maxLength] is the maximum allowed length (default: 50)
  static String? Function(String?) currencyName({
    int maxLength = 50,
    String? errorMessage,
  }) {
    return FormValidators.combine([
      FormValidators.required(errorMessage ?? 'Currency name is required'),
      FormValidators.minLength(2, 'Currency name is too short'),
      FormValidators.maxLength(
        maxLength,
        'Currency name is too long (maximum $maxLength characters)',
      ),
    ]);
  }

  /// Creates a validator for exchange rates
  ///
  /// Ensures the exchange rate is a positive number
  /// [minRate] is the minimum allowed rate (default: 0.00001)
  /// [maxRate] is the optional maximum allowed rate
  static String? Function(String?) exchangeRate({
    double minRate = 0.00001,
    double? maxRate,
    String? errorMessage,
  }) {
    return FormValidators.combine([
      FormValidators.required('Exchange rate is required'),
      FormValidators.money(
        minValue: minRate,
        maxValue: maxRate,
        allowNegative: false,
        errorMessage: errorMessage ?? 'Please enter a valid exchange rate',
      ),
    ]);
  }

  /// Creates a validator that ensures the base and target currency codes are different
  ///
  /// [getBaseCurrencyCode] is a function that returns the current base currency code
  static String? Function(String?) targetCurrencyNotSameAsBase(
    String Function() getBaseCurrencyCode, [
    String errorMessage = 'Base and target currencies must be different',
  ]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      final baseCurrencyCode = getBaseCurrencyCode();
      if (value == baseCurrencyCode) {
        return errorMessage;
      }

      return null;
    };
  }

  /// Creates a validator for currency symbols
  ///
  /// Ensures the currency symbol is valid
  /// [maxLength] is the maximum allowed length (default: 5)
  static String? Function(String?) currencySymbol({
    int maxLength = 5,
    String? errorMessage,
  }) {
    return FormValidators.combine([
      FormValidators.required(errorMessage ?? 'Currency symbol is required'),
      FormValidators.maxLength(
        maxLength,
        'Currency symbol is too long (maximum $maxLength characters)',
      ),
    ]);
  }

  /// Creates a validator for decimal places
  ///
  /// Ensures the decimal places value is between 0 and 6
  static String? Function(String?) decimalPlaces([String? errorMessage]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return 'Decimal places is required';
      }

      final numValue = int.tryParse(value);
      if (numValue == null) {
        return 'Please enter a valid number';
      }

      if (numValue < 0 || numValue > 6) {
        return errorMessage ?? 'Decimal places must be between 0 and 6';
      }

      return null;
    };
  }
}
