import 'package:intl/intl.dart';

/// A collection of validators for date fields
class DateValidators {
  /// Creates a validator that ensures a date is in the correct format
  ///
  /// [format] is the expected date format (e.g., 'yyyy-MM-dd')
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) format(
    String format, [
    String? errorMessage,
  ]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final formatter = DateFormat(format);
        formatter.parseStrict(value);
        return null;
      } catch (e) {
        return errorMessage ?? 'Please enter a valid date in $format format';
      }
    };
  }

  /// Creates a validator that ensures a date is after a minimum date
  ///
  /// [minDate] is the minimum allowed date
  /// [dateFormat] is the format used to parse the input (e.g., 'yyyy-MM-dd')
  /// [errorMessage] is the message to display when validation fails
  /// [inclusive] determines if the minimum date itself is considered valid
  static String? Function(String?) after(
    DateTime minDate,
    String dateFormat, [
    String? errorMessage,
    bool inclusive = false,
  ]) {
    final formatter = DateFormat(dateFormat);
    final formattedMinDate = formatter.format(minDate);

    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final date = formatter.parseStrict(value);

        if (inclusive) {
          if (date.isBefore(minDate)) {
            return errorMessage ?? 'Date must be on or after $formattedMinDate';
          }
        } else {
          if (date.isBefore(minDate) || _isSameDay(date, minDate)) {
            return errorMessage ?? 'Date must be after $formattedMinDate';
          }
        }

        return null;
      } catch (e) {
        // Format error should be caught by the format validator
        return null;
      }
    };
  }

  /// Creates a validator that ensures a date is before a maximum date
  ///
  /// [maxDate] is the maximum allowed date
  /// [dateFormat] is the format used to parse the input (e.g., 'yyyy-MM-dd')
  /// [errorMessage] is the message to display when validation fails
  /// [inclusive] determines if the maximum date itself is considered valid
  static String? Function(String?) before(
    DateTime maxDate,
    String dateFormat, [
    String? errorMessage,
    bool inclusive = false,
  ]) {
    final formatter = DateFormat(dateFormat);
    final formattedMaxDate = formatter.format(maxDate);

    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final date = formatter.parseStrict(value);

        if (inclusive) {
          if (date.isAfter(maxDate)) {
            return errorMessage ??
                'Date must be on or before $formattedMaxDate';
          }
        } else {
          if (date.isAfter(maxDate) || _isSameDay(date, maxDate)) {
            return errorMessage ?? 'Date must be before $formattedMaxDate';
          }
        }

        return null;
      } catch (e) {
        // Format error should be caught by the format validator
        return null;
      }
    };
  }

  /// Creates a validator that ensures a date is between a minimum and maximum date
  ///
  /// [minDate] is the minimum allowed date
  /// [maxDate] is the maximum allowed date
  /// [dateFormat] is the format used to parse the input (e.g., 'yyyy-MM-dd')
  /// [errorMessage] is the message to display when validation fails
  /// [inclusiveMin] determines if the minimum date itself is considered valid
  /// [inclusiveMax] determines if the maximum date itself is considered valid
  static String? Function(String?) between(
    DateTime minDate,
    DateTime maxDate,
    String dateFormat, [
    String? errorMessage,
    bool inclusiveMin = false,
    bool inclusiveMax = false,
  ]) {
    final formatter = DateFormat(dateFormat);
    final formattedMinDate = formatter.format(minDate);
    final formattedMaxDate = formatter.format(maxDate);

    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final date = formatter.parseStrict(value);

        // Check minimum date
        if (inclusiveMin) {
          if (date.isBefore(minDate)) {
            return errorMessage ?? 'Date must be on or after $formattedMinDate';
          }
        } else {
          if (date.isBefore(minDate) || _isSameDay(date, minDate)) {
            return errorMessage ?? 'Date must be after $formattedMinDate';
          }
        }

        // Check maximum date
        if (inclusiveMax) {
          if (date.isAfter(maxDate)) {
            return errorMessage ??
                'Date must be on or before $formattedMaxDate';
          }
        } else {
          if (date.isAfter(maxDate) || _isSameDay(date, maxDate)) {
            return errorMessage ?? 'Date must be before $formattedMaxDate';
          }
        }

        return null;
      } catch (e) {
        // Format error should be caught by the format validator
        return null;
      }
    };
  }

  /// Creates a validator that ensures a date is not on a weekend
  ///
  /// [dateFormat] is the format used to parse the input (e.g., 'yyyy-MM-dd')
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) notWeekend(
    String dateFormat, [
    String errorMessage = 'Weekends are not allowed',
  ]) {
    final formatter = DateFormat(dateFormat);

    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final date = formatter.parseStrict(value);
        final weekday = date.weekday;

        // weekday: 1-7, where 6=Saturday, 7=Sunday
        if (weekday == 6 || weekday == 7) {
          return errorMessage;
        }

        return null;
      } catch (e) {
        // Format error should be caught by the format validator
        return null;
      }
    };
  }

  /// Creates a validator that ensures a date is a business day
  /// (not a weekend and optionally not a holiday)
  ///
  /// [holidays] is a list of holiday dates to exclude
  /// [dateFormat] is the format used to parse the input (e.g., 'yyyy-MM-dd')
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) businessDay(
    String dateFormat, {
    List<DateTime> holidays = const [],
    String errorMessage = 'Date must be a business day',
  }) {
    final formatter = DateFormat(dateFormat);
    final weekendValidator = notWeekend(dateFormat, errorMessage);

    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      // First check if it's a weekend
      final weekendError = weekendValidator(value);
      if (weekendError != null) {
        return weekendError;
      }

      // Then check if it's a holiday
      try {
        final date = formatter.parseStrict(value);

        // Compare only year, month, and day
        for (final holiday in holidays) {
          if (_isSameDay(date, holiday)) {
            return errorMessage;
          }
        }

        return null;
      } catch (e) {
        // Format error should be caught by the format validator
        return null;
      }
    };
  }

  /// Creates a validator that checks if a date is within a specified range
  static String? Function(String?) dateRange(
    DateTime startDate,
    DateTime endDate,
    String format, {
    String? errorMessage,
    bool inclusiveStart = true,
    bool inclusiveEnd = true,
  }) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      try {
        final date = DateFormat(format).parse(value);
        final start = DateTime(startDate.year, startDate.month, startDate.day);
        final end = DateTime(endDate.year, endDate.month, endDate.day);

        final isAfterStart =
            inclusiveStart
                ? date.isAtSameMomentAs(start) || date.isAfter(start)
                : date.isAfter(start);

        final isBeforeEnd =
            inclusiveEnd
                ? date.isAtSameMomentAs(end) || date.isBefore(end)
                : date.isBefore(end);

        if (!isAfterStart || !isBeforeEnd) {
          return errorMessage ??
              'Date must be between ${DateFormat(format).format(start)} and ${DateFormat(format).format(end)}';
        }

        return null;
      } catch (e) {
        return null; // Format error handled separately
      }
    };
  }

  /// Helper function to check if two dates represent the same day
  /// (ignoring time components)
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
