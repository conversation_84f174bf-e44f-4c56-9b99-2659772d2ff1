import 'form_validators.dart';
import 'date_validators.dart';

/// A utility class that provides a cleaner API for combining validators
/// from multiple sources (FormValidators, DateValidators, etc.)
class Validator {
  /// Creates a validator that combines multiple validators
  ///
  /// Returns the first error message from a failing validator, or null if all pass
  static String? Function(String?) compose(
    List<String? Function(String?)> validators,
  ) {
    return FormValidators.combine(validators);
  }

  // Form validation helpers

  /// Creates a validator function that ensures a field is not empty
  static String? Function(String?) required([
    String errorMessage = 'This field is required',
  ]) {
    return FormValidators.required(errorMessage);
  }

  /// Creates a validator function that ensures a field is a valid email address
  static String? Function(String?) email([
    String errorMessage = 'Please enter a valid email address',
  ]) {
    return FormValidators.email(errorMessage);
  }

  /// Creates a validator function that ensures a field has a minimum length
  static String? Function(String?) minLength(
    int minLength, [
    String? errorMessage,
  ]) {
    return FormValidators.minLength(
      minLength,
      errorMessage ?? 'Minimum length is $minLength characters',
    );
  }

  /// Creates a validator function that ensures a field has a maximum length
  static String? Function(String?) maxLength(
    int maxLength, [
    String? errorMessage,
  ]) {
    return FormValidators.maxLength(
      maxLength,
      errorMessage ?? 'Maximum length is $maxLength characters',
    );
  }

  /// Creates a validator function that ensures a field is a valid numeric value
  static String? Function(String?) numeric([
    String errorMessage = 'Please enter a valid number',
  ]) {
    return FormValidators.numeric(errorMessage);
  }

  /// Creates a validator function that ensures a field matches a regular expression
  static String? Function(String?) pattern(
    RegExp pattern,
    String errorMessage,
  ) {
    return FormValidators.pattern(pattern, errorMessage);
  }

  /// Creates a validator for password fields with customizable requirements
  static String? Function(String?) password({
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = true,
    String? errorMessage,
  }) {
    return FormValidators.password(
      minLength: minLength,
      requireUppercase: requireUppercase,
      requireLowercase: requireLowercase,
      requireNumbers: requireNumbers,
      requireSpecialChars: requireSpecialChars,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validator for money/currency input
  static String? Function(String?) money({
    double? minValue,
    double? maxValue,
    bool allowNegative = false,
    String? errorMessage,
  }) {
    return FormValidators.money(
      minValue: minValue,
      maxValue: maxValue,
      allowNegative: allowNegative,
      errorMessage: errorMessage,
    );
  }

  /// Creates a validator that ensures two fields match
  static String? Function(String?) matches(
    String Function() getCompareValue, [
    String errorMessage = 'Fields do not match',
  ]) {
    return FormValidators.matches(getCompareValue, errorMessage);
  }

  // Date validation helpers

  /// Creates a validator that ensures a date is in the correct format
  static String? Function(String?) dateFormat(
    String format, [
    String? errorMessage,
  ]) {
    return DateValidators.format(format, errorMessage);
  }

  /// Creates a validator that ensures a date is after a minimum date
  static String? Function(String?) dateAfter(
    DateTime minDate,
    String dateFormat, [
    String? errorMessage,
  ]) {
    return DateValidators.after(minDate, dateFormat, errorMessage);
  }

  /// Creates a validator that ensures a date is before a maximum date
  static String? Function(String?) dateBefore(
    DateTime maxDate,
    String dateFormat, [
    String? errorMessage,
  ]) {
    return DateValidators.before(maxDate, dateFormat, errorMessage);
  }

  /// Creates a validator that ensures a date is between a minimum and maximum date
  static String? Function(String?) dateBetween(
    DateTime minDate,
    DateTime maxDate,
    String dateFormat, [
    String? errorMessage,
  ]) {
    return DateValidators.between(minDate, maxDate, dateFormat, errorMessage);
  }

  /// Creates a validator that ensures a date is not on a weekend
  static String? Function(String?) notWeekend(
    String dateFormat, [
    String errorMessage = 'Weekends are not allowed',
  ]) {
    return DateValidators.notWeekend(dateFormat, errorMessage);
  }

  /// Creates a validator that ensures a date is a business day
  static String? Function(String?) businessDay(
    String dateFormat, {
    List<DateTime> holidays = const [],
    String errorMessage = 'Date must be a business day',
  }) {
    return DateValidators.businessDay(
      dateFormat,
      holidays: holidays,
      errorMessage: errorMessage,
    );
  }
}
