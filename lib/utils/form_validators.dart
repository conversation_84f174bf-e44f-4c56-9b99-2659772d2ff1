import 'package:intl/intl.dart';

/// A collection of common form validation functions
class FormValidators {
  /// Creates a validator function that ensures a field is not empty
  ///
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) required(String errorMessage) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return errorMessage;
      }
      return null;
    };
  }

  /// Creates a validator function that ensures a field is a valid email address
  ///
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) email([
    String? errorMessage = 'Please enter a valid email address',
  ]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      final emailRegex = RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      );

      if (!emailRegex.hasMatch(value)) {
        return errorMessage;
      }

      return null;
    };
  }

  /// Creates a validator function that ensures a field has a minimum length
  ///
  /// [minLength] is the minimum allowed length
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) minLength(
    int minLength,
    String errorMessage,
  ) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      if (value.length < minLength) {
        return errorMessage;
      }

      return null;
    };
  }

  /// Creates a validator function that ensures a field has a maximum length
  ///
  /// [maxLength] is the maximum allowed length
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) maxLength(
    int maxLength,
    String errorMessage,
  ) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      if (value.length > maxLength) {
        return errorMessage;
      }

      return null;
    };
  }

  /// Creates a validator function that ensures a field is a valid numeric value
  ///
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) numeric([
    String? errorMessage = 'Please enter a valid number',
  ]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      if (double.tryParse(value) == null) {
        return errorMessage;
      }

      return null;
    };
  }

  /// Creates a validator function that ensures a field matches a regular expression
  ///
  /// [pattern] is the regular expression to match
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) pattern(
    RegExp pattern,
    String errorMessage,
  ) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      if (!pattern.hasMatch(value)) {
        return errorMessage;
      }

      return null;
    };
  }

  /// Creates a validator function that combines multiple validators
  ///
  /// Returns the first error message from a failing validator, or null if all pass
  static String? Function(String?) combine(
    List<String? Function(String?)> validators,
  ) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }

      return null;
    };
  }

  /// Creates a validator for password fields with customizable requirements
  ///
  /// [minLength] minimum password length (default: 8)
  /// [requireUppercase] whether uppercase letters are required (default: true)
  /// [requireLowercase] whether lowercase letters are required (default: true)
  /// [requireNumbers] whether numbers are required (default: true)
  /// [requireSpecialChars] whether special characters are required (default: true)
  /// [errorMessage] custom error message to display on validation failure
  static String? Function(String?) password({
    int minLength = 8,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumbers = true,
    bool requireSpecialChars = true,
    String? errorMessage,
  }) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      final List<String> requirements = [];

      if (value.length < minLength) {
        requirements.add('at least $minLength characters');
      }

      if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(value)) {
        requirements.add('an uppercase letter');
      }

      if (requireLowercase && !RegExp(r'[a-z]').hasMatch(value)) {
        requirements.add('a lowercase letter');
      }

      if (requireNumbers && !RegExp(r'[0-9]').hasMatch(value)) {
        requirements.add('a number');
      }

      if (requireSpecialChars &&
          !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
        requirements.add('a special character');
      }

      if (requirements.isNotEmpty) {
        if (errorMessage != null) {
          return errorMessage;
        } else {
          return 'Password must contain ${requirements.join(', ')}';
        }
      }

      return null;
    };
  }

  /// Creates a validator for money/currency input
  ///
  /// [minValue] optional minimum value constraint
  /// [maxValue] optional maximum value constraint
  /// [allowNegative] whether negative values are allowed (default: false)
  /// [errorMessage] optional custom error message
  static String? Function(String?) money({
    double? minValue,
    double? maxValue,
    bool allowNegative = false,
    String? errorMessage,
  }) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      // Clean the input (remove currency symbols and thousand separators)
      final cleanValue = value.replaceAll(RegExp(r'[$,\s]'), '');

      // Try to parse the cleaned value
      final number = double.tryParse(cleanValue);

      if (number == null) {
        return errorMessage ?? 'Please enter a valid amount';
      }

      // Check if negative values are allowed
      if (!allowNegative && number < 0) {
        return errorMessage ?? 'Amount cannot be negative';
      }

      // Check minimum value
      if (minValue != null && number < minValue) {
        return errorMessage ??
            'Amount must be at least ${_formatMoney(minValue)}';
      }

      // Check maximum value
      if (maxValue != null && number > maxValue) {
        return errorMessage ?? 'Amount cannot exceed ${_formatMoney(maxValue)}';
      }

      return null;
    };
  }

  /// Creates a validator that ensures two fields match (e.g., password confirmation)
  ///
  /// [getCompareValue] is a function that returns the value to compare against
  /// [errorMessage] is the message to display when validation fails
  static String? Function(String?) matches(
    String Function() getCompareValue, [
    String errorMessage = 'Fields do not match',
  ]) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return null; // Let required validator handle this case
      }

      final compareValue = getCompareValue();
      if (value != compareValue) {
        return errorMessage;
      }

      return null;
    };
  }

  // Helper method to format money values
  static String _formatMoney(double value) {
    final formatter = NumberFormat.currency(symbol: '\$');
    return formatter.format(value);
  }
}
