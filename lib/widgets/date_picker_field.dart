import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A form field that displays a date and allows selection via a date picker
class DatePickerField extends StatelessWidget {
  /// Label text for the field
  final String labelText;

  /// Currently selected date
  final DateTime selectedDate;

  /// Callback when a new date is selected
  final Function(DateTime?) onDateSelected;

  /// Optional validator function
  final String? Function(String?)? validator;

  /// Date format to display (defaults to yyyy-MM-dd)
  final String dateFormat;

  /// Optional hint text
  final String? hintText;

  /// Creates a new DatePickerField
  const DatePickerField({
    super.key,
    required this.labelText,
    required this.selectedDate,
    required this.onDateSelected,
    this.validator,
    this.dateFormat = 'yyyy-MM-dd',
    this.hintText,
  });

  @override
  Widget build(BuildContext context) {
    final controller = TextEditingController(
      text: DateFormat(dateFormat).format(selectedDate),
    );

    return TextFormField(
      controller: controller,
      readOnly: true,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText ?? 'Select date',
        border: const OutlineInputBorder(),
        suffixIcon: const Icon(Icons.calendar_today),
      ),
      validator: validator,
      onTap: () async {
        // Close the keyboard if it's open
        FocusScope.of(context).requestFocus(FocusNode());

        // Show date picker
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: selectedDate,
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
        );

        if (pickedDate != null) {
          // Update the display
          controller.text = DateFormat(dateFormat).format(pickedDate);

          // Call the callback
          onDateSelected(pickedDate);
        }
      },
    );
  }
}
