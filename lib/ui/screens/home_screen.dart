import 'package:flutter/material.dart';
import 'package:we_like_money/examples/forms/expense_form_example.dart';
import 'package:we_like_money/examples/forms/vendor_invoice_form_example.dart';
import 'package:we_like_money/screens/account/account_list_screen_refactored.dart';
import 'package:we_like_money/screens/general_ledger/general_ledger_screen_refactored.dart';
import 'package:we_like_money/screens/vendor/vendor_list_screen.dart';
import 'package:we_like_money/screens/vendor_invoice/vendor_invoice_list_screen.dart';
import 'package:we_like_money/ui/screens/companies_screen.dart';
import 'package:we_like_money/ui/screens/expenses_screen.dart';
import 'package:we_like_money/ui/screens/projects_screen.dart';
import 'package:we_like_money/ui/screens/settings_screen.dart';
import 'package:we_like_money/ui/widgets/company_selector.dart';
import 'package:we_like_money/ui/widgets/connection_status.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('We Like Money'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // Connection status indicator
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Center(child: ConnectionStatusWidget()),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Column(
        children: [
          const SizedBox(height: 16),
          // Company selector at the top
          const CompanySelector(),
          const SizedBox(height: 16),

          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    const Text(
                      'Welcome to We Like Money',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text('Your accounting solution'),
                    const SizedBox(height: 40),

                    // Navigation buttons section
                    const Text(
                      'Quick Access',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Grid of navigation buttons
                    GridView.count(
                      shrinkWrap: true,
                      crossAxisCount: 3,
                      childAspectRatio: 3.0,
                      mainAxisSpacing: 12,
                      crossAxisSpacing: 12,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        // Companies button
                        _buildGridButton(
                          context: context,
                          icon: Icons.business,
                          label: 'Companies',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const CompaniesScreen(),
                              ),
                            );
                          },
                        ),

                        // Projects button
                        _buildGridButton(
                          context: context,
                          icon: Icons.folder_special,
                          label: 'Projects',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ProjectsScreen(),
                              ),
                            );
                          },
                        ),

                        // Accounts button
                        _buildGridButton(
                          context: context,
                          icon: Icons.account_balance,
                          label: 'Accounts',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const AccountListScreenRefactored(),
                              ),
                            );
                          },
                        ),

                        // Vendors button
                        _buildGridButton(
                          context: context,
                          icon: Icons.business_center,
                          label: 'Vendors',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const VendorListScreen(),
                              ),
                            );
                          },
                        ),

                        // Vendor Invoices button
                        _buildGridButton(
                          context: context,
                          icon: Icons.receipt,
                          label: 'Vendor Invoices',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const VendorInvoiceListScreen(),
                              ),
                            );
                          },
                        ),

                        // Expenses button
                        _buildGridButton(
                          context: context,
                          icon: Icons.receipt_long,
                          label: 'Expenses',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ExpensesScreen(),
                              ),
                            );
                          },
                        ),

                        // General Ledger button
                        _buildGridButton(
                          context: context,
                          icon: Icons.book,
                          label: 'General Ledger',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const GeneralLedgerScreenRefactored(),
                              ),
                            );
                          },
                        ),

                        // Settings button
                        _buildGridButton(
                          context: context,
                          icon: Icons.settings,
                          label: 'Settings',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsScreen(),
                              ),
                            );
                          },
                        ),

                        // Invoice Form Example button
                        _buildGridButton(
                          context: context,
                          icon: Icons.receipt,
                          label: 'Invoice Form',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const VendorInvoiceFormExample(),
                              ),
                            );
                          },
                        ),

                        // Expense Form Example button
                        _buildGridButton(
                          context: context,
                          icon: Icons.money,
                          label: 'Expense Form',
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => const ExpenseFormExample(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ), // Add space at the bottom for scrolling
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a grid button with icon and label
  Widget _buildGridButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              label,
              style: const TextStyle(fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
