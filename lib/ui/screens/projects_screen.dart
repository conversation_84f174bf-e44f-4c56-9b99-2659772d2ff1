import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/models/project.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/ui/widgets/company_selector.dart';

/// Screen for displaying and managing projects
class ProjectsScreen extends ConsumerWidget {
  const ProjectsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the projects provider to get the AsyncValue
    final projectsAsync = ref.watch(projectsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Projects'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddProjectDialog(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          const SizedBox(height: 16),
          // Company selector at the top
          const CompanySelector(),
          const SizedBox(height: 16),

          Expanded(
            child: projectsAsync.when(
              // Show loading indicator while data is loading
              loading: () => const Center(child: CircularProgressIndicator()),
              // Show error message if there's an error
              error:
                  (error, stackTrace) => Center(
                    child: Text(
                      'Error loading projects: $error',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
              // Show the list of projects when data is available
              data: (projects) {
                if (projects.isEmpty) {
                  return const Center(
                    child: Text('No projects found. Add one to get started.'),
                  );
                }

                return ListView.builder(
                  itemCount: projects.length,
                  itemBuilder: (context, index) {
                    final project = projects[index];
                    return ProjectListItem(
                      project: project,
                      onEdit:
                          () => _showEditProjectDialog(context, ref, project),
                      onDelete:
                          () => _showDeleteProjectDialog(context, ref, project),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Shows a dialog for adding a new project
  void _showAddProjectDialog(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    String projectCode = '';
    String projectName = '';
    String? description;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Project'),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Project Code',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a project code';
                      }
                      return null;
                    },
                    onSaved: (value) => projectCode = value!,
                  ),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Project Name',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a project name';
                      }
                      return null;
                    },
                    onSaved: (value) => projectName = value!,
                  ),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                    ),
                    onSaved: (value) => description = value,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();

                    // Create a new project with a temporary ID
                    // The actual ID will be assigned by the database
                    final newProject = Project(
                      projectId: 0, // Temporary ID
                      projectCode: projectCode,
                      projectName: projectName,
                      description: description,
                    );

                    try {
                      // Use the creation provider to create the project
                      final createProject = ref.read(projectCreationProvider);
                      await createProject(newProject);

                      // Refresh the projects list and use the result
                      final _ = await ref.refresh(projectsProvider.future);

                      // Close the dialog
                      if (context.mounted) {
                        Navigator.of(context).pop();

                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Project created successfully'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    } catch (e) {
                      // Show error message
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Failed to create project: ${e.toString()}',
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  }
                },
                child: const Text('Add'),
              ),
            ],
          ),
    );
  }

  /// Shows a dialog for editing a project
  void _showEditProjectDialog(
    BuildContext context,
    WidgetRef ref,
    Project project,
  ) {
    final formKey = GlobalKey<FormState>();
    String projectCode = project.projectCode;
    String projectName = project.projectName;
    String? description = project.description;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Edit Project'),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    initialValue: projectCode,
                    decoration: const InputDecoration(
                      labelText: 'Project Code',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a project code';
                      }
                      return null;
                    },
                    onSaved: (value) => projectCode = value!,
                  ),
                  TextFormField(
                    initialValue: projectName,
                    decoration: const InputDecoration(
                      labelText: 'Project Name',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a project name';
                      }
                      return null;
                    },
                    onSaved: (value) => projectName = value!,
                  ),
                  TextFormField(
                    initialValue: description,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                    ),
                    onSaved: (value) => description = value,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();

                    // Create an updated project with the same ID
                    final updatedProject = Project(
                      projectId: project.projectId,
                      projectCode: projectCode,
                      projectName: projectName,
                      description: description,
                    );

                    try {
                      // Use the update provider to update the project
                      final updateProject = ref.read(projectUpdateProvider);
                      await updateProject(updatedProject);

                      // Refresh the projects list and use the result
                      final _ = await ref.refresh(projectsProvider.future);

                      // Close the dialog
                      if (context.mounted) {
                        Navigator.of(context).pop();

                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Project updated successfully'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    } catch (e) {
                      // Show error message
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Failed to update project: ${e.toString()}',
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  }
                },
                child: const Text('Update'),
              ),
            ],
          ),
    );
  }

  /// Shows a confirmation dialog for deleting a project
  void _showDeleteProjectDialog(
    BuildContext context,
    WidgetRef ref,
    Project project,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Project'),
            content: Text(
              'Are you sure you want to delete "${project.projectName}"? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  try {
                    // Use the deletion provider to delete the project
                    final deleteProject = ref.read(projectDeletionProvider);
                    await deleteProject(project.projectId);

                    // Refresh the projects list and use the result
                    final _ = await ref.refresh(projectsProvider.future);

                    // Close the dialog
                    if (context.mounted) {
                      Navigator.of(context).pop();

                      // Show success message
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Project deleted successfully'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    // Show error message
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Failed to delete project: ${e.toString()}',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}

/// Widget for displaying a project in the list
class ProjectListItem extends ConsumerWidget {
  final Project project;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const ProjectListItem({
    super.key,
    required this.project,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(project.projectName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Code: ${project.projectCode}'),
            if (project.description != null)
              Text(
                'Description: ${project.description}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(icon: const Icon(Icons.edit), onPressed: onEdit),
            IconButton(icon: const Icon(Icons.delete), onPressed: onDelete),
          ],
        ),
        onTap: () {
          // Navigate to project details screen
          // This would be implemented in a real app
        },
      ),
    );
  }
}
