import 'package:flutter/material.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

class AccountDetailsScreen extends StatefulWidget {
  final String accountNumber;

  const AccountDetailsScreen({super.key, required this.accountNumber});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  // Get the AccountViewModel from the dependency injection container
  final AccountViewModel _viewModel = getIt<AccountViewModel>();
  Account? _account;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAccount();
  }

  Future<void> _loadAccount() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final account = await _viewModel.getAccountByNumber(widget.accountNumber);
      setState(() {
        _account = account;
        _isLoading = false;
      });
    } on BusinessException catch (e) {
      setState(() {
        _error = e.message;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'An unexpected error occurred';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Details'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error: $_error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadAccount,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _account == null
              ? const Center(child: Text('Account not found'))
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Account Name',
                      style: Theme.of(context).textTheme.labelLarge,
                    ),
                    Text(
                      _account!.accountName,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Account Number',
                      style: Theme.of(context).textTheme.labelLarge,
                    ),
                    Text(
                      _account!.accountNumber,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                  ],
                ),
              ),
    );
  }
}
