import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/expense.dart';
import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/providers/expense_provider.dart';
import 'package:we_like_money/ui/screens/expense/expense_entry_screen.dart';

/// Screen for displaying and managing expenses
class ExpensesScreen extends ConsumerWidget {
  const ExpensesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expensesAsync = ref.watch(expensesProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Expenses')),
      body: expensesAsync.when(
        data: (expenses) {
          if (expenses.isEmpty) {
            return const Center(
              child: Text('No expenses found. Add one to get started.'),
            );
          }

          return ListView.builder(
            itemCount: expenses.length,
            itemBuilder: (context, index) {
              final expense = expenses[index];
              return ExpenseListItem(expense: expense);
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => Center(
              child: Text(
                'Error loading expenses: $error',
                style: const TextStyle(color: Colors.red),
              ),
            ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToExpenseEntry(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Navigate to the expense entry screen
  void _navigateToExpenseEntry(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ExpenseEntryScreen()));
  }
}

/// List item for displaying an expense
class ExpenseListItem extends ConsumerWidget {
  final Expense expense;

  const ExpenseListItem({super.key, required this.expense});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currencyFormat = NumberFormat.currency(
      symbol: expense.currencyCode,
      decimalDigits: 2,
    );

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ListTile(
        title: Text(
          'Vendor ID: ${expense.vendorId}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Date: ${DateFormat('yyyy-MM-dd').format(expense.expenseDate)}',
            ),
            Text('Amount: ${currencyFormat.format(expense.amount)}'),
            Text(
              'Payment Method: ${PaymentMethodExtension(expense.paymentMethod).displayName}',
            ),
            if (expense.projectId != null)
              Text('Project ID: ${expense.projectId}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _editExpense(context, ref),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteExpenseDialog(context, ref),
            ),
          ],
        ),
        isThreeLine: true, // Give more space for multiple subtitle lines
        onTap: () => _editExpense(context, ref),
      ),
    );
  }

  /// Navigate to the expense entry screen for editing
  void _editExpense(BuildContext context, WidgetRef ref) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ExpenseEntryScreen(expenseId: expense.expenseId),
      ),
    );
  }

  /// Show dialog to confirm expense deletion
  Future<void> _showDeleteExpenseDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Expense'),
          content: const Text(
            'Are you sure you want to delete this expense? '
            'This action cannot be undone.',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  final deleteExpense = ref.read(expenseDeletionProvider);
                  await deleteExpense(expense.expenseId);

                  // Refresh expenses list and use the result
                  final _ = await ref.refresh(expensesProvider.future);

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Expense deleted'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    Navigator.of(context).pop();
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error deleting expense: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    Navigator.of(context).pop();
                  }
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
