import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/project_provider.dart';
import 'package:we_like_money/providers/staff_member_provider.dart';
import 'package:we_like_money/ui/screens/expense/components/form_section_header.dart';
import 'package:we_like_money/ui/screens/expense/expense_view_model.dart';
import 'package:we_like_money/ui/widgets/form_fields.dart';

/// A form section for project and staff selection
class ProjectStaffSection extends ConsumerWidget {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  const ProjectStaffSection({
    required this.viewModel,
    super.key,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final projectsAsync = ref.watch(projectsProvider);
    final staffMembersAsync = ref.watch(staffMembersProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const FormSectionHeader(title: 'Project and Staff'),
        
        // Project field (optional)
        projectsAsync.when(
          data: (projects) {
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedProjectId,
              builder: (context, selectedProjectId, _) {
                return OptionalDropdownFormField<int>(
                  value: selectedProjectId,
                  items: projects.map((p) => p.projectId).toList(),
                  onChanged: (value) {
                    viewModel.selectedProjectId.value = value;
                  },
                  label: 'Project (Optional)',
                  displayString: (value) => projects
                      .firstWhere(
                        (p) => p.projectId == value,
                      )
                      .projectName,
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, _) => const Text(
            'Failed to load projects',
            style: TextStyle(color: Colors.red),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Staff member field (optional)
        staffMembersAsync.when(
          data: (staffMembers) {
            return ValueListenableBuilder(
              valueListenable: viewModel.selectedStaffId,
              builder: (context, selectedStaffId, _) {
                return OptionalDropdownFormField<int>(
                  value: selectedStaffId,
                  items: staffMembers.map((s) => s.staffId).toList(),
                  onChanged: (value) {
                    viewModel.selectedStaffId.value = value;
                  },
                  label: 'Staff Member (Optional)',
                  displayString: (value) => staffMembers
                      .firstWhere((s) => s.staffId == value)
                      .staffName,
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, _) => const Text(
            'Failed to load staff members',
            style: TextStyle(color: Colors.red),
          ),
        ),
      ],
    );
  }
}
