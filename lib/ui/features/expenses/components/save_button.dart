import 'package:flutter/material.dart';
import 'package:we_like_money/ui/screens/expense/expense_view_model.dart';

/// A button for saving the expense
class SaveButton extends StatelessWidget {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Callback when the save is successful
  final VoidCallback onSaveSuccess;
  
  /// Constructor
  const SaveButton({
    required this.viewModel,
    required this.onSaveSuccess,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: viewModel.isLoading,
      builder: (context, isLoading, _) {
        return ElevatedButton(
          onPressed: isLoading
              ? null
              : () async {
                  final success = await viewModel.saveExpense(context);
                  if (success && context.mounted) {
                    onSaveSuccess();
                  }
                },
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: Text(
            viewModel.isEditMode ? 'Update Expense' : 'Save Expense',
            style: const TextStyle(fontSize: 16),
          ),
        );
      },
    );
  }
}
