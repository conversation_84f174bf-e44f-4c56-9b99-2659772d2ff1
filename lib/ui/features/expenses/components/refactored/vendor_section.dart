import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';
import 'package:we_like_money/ui/forms/form_section.dart';
import 'package:we_like_money/ui/screens/expense/expense_view_model.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A refactored form section for vendor selection
class VendorSectionRefactored extends ConsumerWidget {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  const VendorSectionRefactored({
    required this.viewModel,
    super.key,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _VendorSectionContent(
      viewModel: viewModel,
      ref: ref,
    );
  }
}

/// Content widget for the vendor section
class _VendorSectionContent extends FormSection {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// The provider reference
  final WidgetRef ref;
  
  /// Constructor
  const _VendorSectionContent({
    required this.viewModel,
    required this.ref,
  }) : super(
          title: 'Vendor',
          helpText: 'Enter the vendor ID or name',
          headerIcon: Icons.business,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Vendor ID field
        AppTextFormField(
          labelText: 'Vendor ID',
          value: viewModel.vendorController.text,
          onChanged: (value) {
            viewModel.vendorController.text = value;
          },
          isRequired: true,
          validationRule: Validator.required('Vendor ID is required'),
          prefixIcon: Icons.business,
          hintText: 'Enter vendor ID or name',
          helpText: 'Enter the vendor ID or name for this expense',
        ),
        
        // Vendor lookup button
        Align(
          alignment: Alignment.centerRight,
          child: TextButton.icon(
            icon: const Icon(Icons.search),
            label: const Text('Look up Vendor'),
            onPressed: () => _showVendorLookupDialog(context),
          ),
        ),
      ],
    );
  }
  
  /// Show a dialog to look up vendors
  Future<void> _showVendorLookupDialog(BuildContext context) async {
    final vendorsAsync = await ref.read(vendorsProvider.future);
    
    if (!context.mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select a Vendor'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: vendorsAsync.length,
            itemBuilder: (context, index) {
              final vendor = vendorsAsync[index];
              return ListTile(
                title: Text(vendor.vendorName),
                subtitle: Text('ID: ${vendor.vendorId}'),
                onTap: () {
                  viewModel.vendorController.text = vendor.vendorId;
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
