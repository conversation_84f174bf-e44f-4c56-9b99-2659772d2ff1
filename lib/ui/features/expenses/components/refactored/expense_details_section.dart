import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:we_like_money/providers/currency_provider.dart';
import 'package:we_like_money/ui/forms/fields/currency_form_field.dart';
import 'package:we_like_money/ui/forms/fields/date_form_field.dart';
import 'package:we_like_money/ui/forms/fields/dropdown_form_field.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';
import 'package:we_like_money/ui/forms/form_section.dart';
import 'package:we_like_money/ui/features/expenses/expense_view_model.dart';
import 'package:we_like_money/utils/validation/index.dart';
import 'package:we_like_money/widgets/error_display.dart';

/// A refactored form section for expense details
class ExpenseDetailsSectionRefactored extends ConsumerWidget {
  /// The view model
  final ExpenseViewModel viewModel;

  /// Constructor
  const ExpenseDetailsSectionRefactored({required this.viewModel, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _ExpenseDetailsSectionContent(viewModel: viewModel, ref: ref);
  }
}

/// Content widget for the expense details section
class _ExpenseDetailsSectionContent extends FormSection {
  /// The view model
  final ExpenseViewModel viewModel;

  /// The provider reference
  final WidgetRef ref;

  /// Constructor
  const _ExpenseDetailsSectionContent({
    required this.viewModel,
    required this.ref,
  }) : super(
         title: 'Expense Details',
         helpText:
             'Enter the expense details including date, amount, and currency',
         headerIcon: Icons.receipt,
       );

  @override
  Widget buildSectionContent(BuildContext context) {
    // Watch currencies
    final currenciesAsync = ref.watch(currenciesProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Expense date field
        ValueListenableBuilder(
          valueListenable: viewModel.expenseDate,
          builder: (context, expenseDate, _) {
            return AppDateFormField(
              labelText: 'Expense Date',
              value: expenseDate,
              onChanged: (date) {
                viewModel.expenseDate.value = date;
              },
              isRequired: true,
              validationRule: Validator.required('Expense date is required'),
            );
          },
        ),

        const SizedBox(height: 16),

        // Amount field
        AppCurrencyFormField.invoiceAmount(
          labelText: 'Amount',
          value: viewModel.amountController.text,
          onChanged: (value) {
            viewModel.amountController.text = value;
          },
          isRequired: true,
        ),

        const SizedBox(height: 16),

        // Currency field
        currenciesAsync.when(
          data: (currencies) {
            if (currencies.isEmpty) {
              return _buildCurrencyTextField();
            }

            return _buildCurrencyDropdown(currencies);
          },
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          error:
              (error, _) => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ErrorDisplay(
                    errorMessage: 'Error loading currencies: $error',
                    onRetry: () => ref.refresh(currenciesProvider),
                  ),
                  const SizedBox(height: 16),
                  _buildCurrencyTextField(),
                ],
              ),
        ),

        const SizedBox(height: 16),

        // Tax amount field
        AppCurrencyFormField.taxAmount(
          labelText: 'Tax Amount',
          value: viewModel.taxAmountController.text,
          onChanged: (value) {
            viewModel.taxAmountController.text = value;
          },
          invoiceAmount: viewModel.amountController.text,
          isRequired: false,
        ),
      ],
    );
  }

  /// Build a text field for currency input when no currencies are available
  Widget _buildCurrencyTextField() {
    return ValueListenableBuilder(
      valueListenable: viewModel.selectedCurrencyCode,
      builder: (context, selectedCurrencyCode, _) {
        return AppTextFormField(
          labelText: 'Currency Code',
          value: selectedCurrencyCode ?? '',
          onChanged: (value) {
            viewModel.selectedCurrencyCode.value = value;
          },
          isRequired: true,
          validationRule: Validator.required('Currency code is required'),
          prefixIcon: Icons.currency_exchange,
          hintText: 'e.g., USD',
        );
      },
    );
  }

  /// Build a dropdown for currency selection
  Widget _buildCurrencyDropdown(List<dynamic> currencies) {
    return ValueListenableBuilder(
      valueListenable: viewModel.selectedCurrencyCode,
      builder: (context, selectedCurrencyCode, _) {
        return AppDropdownFormField<String>.required(
          value: selectedCurrencyCode,
          onChanged: (value) {
            if (value != null) {
              viewModel.selectedCurrencyCode.value = value;
            }
          },
          items: currencies.map((c) => c.currencyCode as String).toList(),
          displayStringBuilder: (code) {
            final currency = currencies.firstWhere(
              (c) => c.currencyCode == code,
              orElse: () => null,
            );
            return currency != null
                ? '${currency.currencyCode} - ${currency.currencyName}'
                : code;
          },
          labelText: 'Currency',
          prefixIcon: Icons.currency_exchange,
          errorMessage: 'Please select a currency',
        );
      },
    );
  }
}
