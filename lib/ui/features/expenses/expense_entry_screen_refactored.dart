import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/ui/forms/index.dart';
import 'package:we_like_money/ui/screens/expense/components/refactored/expense_details_section.dart';
import 'package:we_like_money/ui/screens/expense/components/refactored/payment_details_section.dart';
import 'package:we_like_money/ui/screens/expense/components/refactored/project_staff_section.dart';
import 'package:we_like_money/ui/screens/expense/components/refactored/vendor_section.dart';
import 'package:we_like_money/ui/screens/expense/expense_form_state.dart';
import 'package:we_like_money/ui/screens/expense/expense_view_model.dart';
import 'package:we_like_money/widgets/loading_display.dart';

/// Screen for entering and editing expense details using the form component library
class ExpenseEntryScreenRefactored extends HookConsumerWidget {
  /// ID of the expense to edit, or null for a new expense
  final int? expenseId;

  /// Constructor
  const ExpenseEntryScreenRefactored({super.key, this.expenseId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Create a view model instance
    final viewModel = useMemoized(
      () => ExpenseViewModel(ref, expenseId),
      [expenseId],
    );
    
    // Create a form state manager
    final formStateManager = useMemoized(
      () => ExpenseFormState(viewModel),
      [viewModel],
    );

    // Dispose resources when the widget is disposed
    useEffect(() {
      return () {
        viewModel.dispose();
        formStateManager.dispose();
      };
    }, [viewModel, formStateManager]);

    // Handle form submission
    Future<void> onSubmit() async {
      final success = await viewModel.saveExpense(context);
      if (success && context.mounted) {
        Navigator.of(context).pop();
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(expenseId != null ? 'Edit Expense' : 'New Expense'),
      ),
      body: ValueListenableBuilder(
        valueListenable: viewModel.isLoading,
        builder: (context, isLoading, _) {
          if (isLoading) {
            return const LoadingDisplay(message: 'Processing expense...');
          }

          return _buildForm(
            context, 
            viewModel, 
            formStateManager, 
            onSubmit,
          );
        },
      ),
    );
  }
  
  /// Build the form using FormBuilder
  Widget _buildForm(
    BuildContext context,
    ExpenseViewModel viewModel,
    ExpenseFormState formState,
    VoidCallback onSubmit,
  ) {
    // Create a form builder with the form key from the view model
    final formBuilder = FormBuilder(formKey: viewModel.formKey);
    
    // Build the form with all sections
    return formBuilder
      // Error message section
      .addSection(_buildErrorSection(viewModel))
      
      // Vendor section
      .addSection(
        VendorSectionRefactored(
          viewModel: viewModel,
        ),
      )
      
      // Expense details section
      .addSection(
        ExpenseDetailsSectionRefactored(
          viewModel: viewModel,
        ),
      )
      
      // Project and staff section
      .addSection(
        ProjectStaffSectionRefactored(
          viewModel: viewModel,
        ),
      )
      
      // Payment details section
      .addSection(
        PaymentDetailsSectionRefactored(
          viewModel: viewModel,
        ),
      )
      
      // Submit button
      .addSubmitButton(
        text: viewModel.isEditMode ? 'Update Expense' : 'Create Expense',
        onPressed: onSubmit,
        isLoading: viewModel.isLoading.value,
        backgroundColor: Theme.of(context).primaryColor,
        textColor: Colors.white,
        height: 50,
      )
      
      // Build the form inside a scrollable container
      .buildScrollable(context);
  }
  
  /// Build the error display section
  Widget _buildErrorSection(ExpenseViewModel viewModel) {
    return ValueListenableBuilder(
      valueListenable: viewModel.errorMessage,
      builder: (context, errorMessage, _) {
        if (errorMessage == null) {
          return const SizedBox.shrink();
        }
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Card(
            color: Colors.red.shade100,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.error, color: Colors.red.shade900),
                      const SizedBox(width: 8),
                      Text(
                        'Error',
                        style: TextStyle(
                          color: Colors.red.shade900,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage,
                    style: TextStyle(color: Colors.red.shade900),
                  ),
                  const SizedBox(height: 8),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: () {
                        viewModel.errorMessage.value = null;
                      },
                      child: const Text('Dismiss'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
