import 'package:we_like_money/models/payment_method.dart';
import 'package:we_like_money/ui/forms/form_state_manager.dart';
import 'package:we_like_money/ui/screens/expense/expense_view_model.dart';

/// Form state manager for the expense form
class ExpenseFormState extends FormStateManager {
  /// The view model
  final ExpenseViewModel viewModel;
  
  /// Constructor
  ExpenseFormState(this.viewModel) : super(formKey: viewModel.formKey) {
    // Initialize form state with values from the view model
    _initializeFormState();
    
    // Setup field dependencies
    _setupFieldDependencies();
  }
  
  /// Initialize form state with values from the view model
  void _initializeFormState() {
    // Set original values
    setOriginalValue('vendorId', viewModel.vendorController.text);
    setOriginalValue('expenseDate', viewModel.expenseDate.value);
    setOriginalValue('amount', viewModel.amountController.text);
    setOriginalValue('taxAmount', viewModel.taxAmountController.text);
    setOriginalValue('currencyCode', viewModel.selectedCurrencyCode.value);
    setOriginalValue('projectId', viewModel.selectedProjectId.value);
    setOriginalValue('staffId', viewModel.selectedStaffId.value);
    setOriginalValue('paymentMethod', viewModel.selectedPaymentMethod.value);
    setOriginalValue('creditCardNumber', viewModel.creditCardNumberController.text);
    
    // Setup listeners for view model value notifiers
    viewModel.expenseDate.addListener(_onExpenseDateChanged);
    viewModel.selectedCurrencyCode.addListener(_onCurrencyCodeChanged);
    viewModel.selectedProjectId.addListener(_onProjectIdChanged);
    viewModel.selectedStaffId.addListener(_onStaffIdChanged);
    viewModel.selectedPaymentMethod.addListener(_onPaymentMethodChanged);
    
    // Setup listeners for text controllers
    viewModel.vendorController.addListener(_onVendorIdChanged);
    viewModel.amountController.addListener(_onAmountChanged);
    viewModel.taxAmountController.addListener(_onTaxAmountChanged);
    viewModel.creditCardNumberController.addListener(_onCreditCardNumberChanged);
  }
  
  /// Setup field dependencies
  void _setupFieldDependencies() {
    // Credit card number depends on payment method
    addFieldDependency('creditCardNumber', 'paymentMethod');
    
    // Tax amount validation depends on amount
    addFieldDependency('taxAmount', 'amount');
  }
  
  // Listener callbacks
  
  void _onVendorIdChanged() {
    setCurrentValue('vendorId', viewModel.vendorController.text);
  }
  
  void _onExpenseDateChanged() {
    setCurrentValue('expenseDate', viewModel.expenseDate.value);
  }
  
  void _onAmountChanged() {
    setCurrentValue('amount', viewModel.amountController.text);
  }
  
  void _onTaxAmountChanged() {
    setCurrentValue('taxAmount', viewModel.taxAmountController.text);
  }
  
  void _onCurrencyCodeChanged() {
    setCurrentValue('currencyCode', viewModel.selectedCurrencyCode.value);
  }
  
  void _onProjectIdChanged() {
    setCurrentValue('projectId', viewModel.selectedProjectId.value);
  }
  
  void _onStaffIdChanged() {
    setCurrentValue('staffId', viewModel.selectedStaffId.value);
  }
  
  void _onPaymentMethodChanged() {
    setCurrentValue('paymentMethod', viewModel.selectedPaymentMethod.value);
    
    // Clear credit card number if payment method is not credit card
    if (viewModel.selectedPaymentMethod.value != PaymentMethod.creditCard) {
      viewModel.creditCardNumberController.text = '';
    }
  }
  
  void _onCreditCardNumberChanged() {
    setCurrentValue('creditCardNumber', viewModel.creditCardNumberController.text);
  }
  
  @override
  void dispose() {
    // Remove listeners from view model value notifiers
    viewModel.expenseDate.removeListener(_onExpenseDateChanged);
    viewModel.selectedCurrencyCode.removeListener(_onCurrencyCodeChanged);
    viewModel.selectedProjectId.removeListener(_onProjectIdChanged);
    viewModel.selectedStaffId.removeListener(_onStaffIdChanged);
    viewModel.selectedPaymentMethod.removeListener(_onPaymentMethodChanged);
    
    // Remove listeners from text controllers
    viewModel.vendorController.removeListener(_onVendorIdChanged);
    viewModel.amountController.removeListener(_onAmountChanged);
    viewModel.taxAmountController.removeListener(_onTaxAmountChanged);
    viewModel.creditCardNumberController.removeListener(_onCreditCardNumberChanged);
    
    super.dispose();
  }
}
