import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/models/vendor_invoice.dart';
import 'package:we_like_money/providers/vendor_invoice_provider.dart';
import 'package:we_like_money/screens/vendor_invoice/components/invoice_filter_dialog.dart';
import 'package:we_like_money/screens/vendor_invoice/components/invoice_list_item.dart';
import 'package:we_like_money/screens/vendor_invoice/components/invoice_search_bar.dart';
import 'package:we_like_money/screens/vendor_invoice/vendor_invoice_entry_screen.dart';
import 'package:we_like_money/widgets/error_display.dart';
import 'package:we_like_money/widgets/loading_display.dart';

/// A screen that displays a list of vendor invoices with filtering options
class VendorInvoiceListScreen extends ConsumerStatefulWidget {
  /// Creates a vendor invoice list screen
  const VendorInvoiceListScreen({super.key});

  @override
  ConsumerState<VendorInvoiceListScreen> createState() =>
      _VendorInvoiceListScreenState();
}

class _VendorInvoiceListScreenState
    extends ConsumerState<VendorInvoiceListScreen> {
  // Filter state
  String _searchQuery = '';
  bool _showPaidOnly = false;
  bool _showUnpaidOnly = true;
  Vendor? _selectedVendor;

  // Formatters
  final _dateFormat = DateFormat('yyyy-MM-dd');
  final _currencyFormat = NumberFormat.currency(symbol: '\$');

  @override
  Widget build(BuildContext context) {
    final vendorInvoicesAsync = ref.watch(vendorInvoicesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Vendor Invoices'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter Invoices',
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          InvoiceSearchBar(
            searchQuery: _searchQuery,
            onSearchChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
            },
          ),

          // Active filters display
          if (_showPaidOnly || _showUnpaidOnly || _selectedVendor != null)
            _buildActiveFiltersDisplay(),

          // Invoices list
          Expanded(
            child: vendorInvoicesAsync.when(
              loading:
                  () => const LoadingDisplay(message: 'Loading invoices...'),
              error:
                  (error, _) => ErrorDisplay(
                    errorMessage: 'Error loading invoices: $error',
                    onRetry: () => ref.refresh(vendorInvoicesProvider),
                  ),
              data: (invoices) {
                final filteredInvoices = _filterInvoices(invoices);

                if (filteredInvoices.isEmpty) {
                  return _buildEmptyState();
                }

                return _buildInvoiceList(filteredInvoices);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const VendorInvoiceEntryScreen(),
            ),
          );
        },
        tooltip: 'Add Invoice',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build the active filters display
  Widget _buildActiveFiltersDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.grey.shade100,
      child: Row(
        children: [
          const Text(
            'Active Filters:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          if (_showPaidOnly)
            Chip(
              label: const Text('Paid'),
              onDeleted: () {
                setState(() {
                  _showPaidOnly = false;
                  if (!_showUnpaidOnly) {
                    _showUnpaidOnly = true;
                  }
                });
              },
            ),
          if (_showUnpaidOnly)
            Padding(
              padding: const EdgeInsets.only(left: 4.0),
              child: Chip(
                label: const Text('Unpaid'),
                onDeleted: () {
                  setState(() {
                    _showUnpaidOnly = false;
                    if (!_showPaidOnly) {
                      _showPaidOnly = true;
                    }
                  });
                },
              ),
            ),
          if (_selectedVendor != null)
            Padding(
              padding: const EdgeInsets.only(left: 4.0),
              child: Chip(
                label: Text('Vendor: ${_selectedVendor!.vendorName}'),
                onDeleted: () {
                  setState(() {
                    _selectedVendor = null;
                  });
                },
              ),
            ),
        ],
      ),
    );
  }

  /// Build the empty state widget
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.receipt_long, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'No invoices found',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedVendor != null
                ? 'Try changing your search or filters'
                : 'Create your first invoice by tapping the + button',
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Build the invoice list
  Widget _buildInvoiceList(List<VendorInvoice> invoices) {
    return ListView.builder(
      itemCount: invoices.length,
      itemBuilder: (context, index) {
        final invoice = invoices[index];
        return InvoiceListItem(
          invoice: invoice,
          dateFormat: _dateFormat,
          currencyFormat: _currencyFormat,
        );
      },
    );
  }

  /// Filter invoices based on current filters
  List<VendorInvoice> _filterInvoices(List<VendorInvoice> invoices) {
    return invoices.where((invoice) {
      // Filter by payment status
      if (_showPaidOnly && !(invoice.isPaid ?? false)) {
        return false;
      }
      if (_showUnpaidOnly && (invoice.isPaid ?? false)) {
        return false;
      }

      // Filter by vendor if one is selected
      if (_selectedVendor != null &&
          invoice.vendorId != _selectedVendor!.vendorId) {
        return false;
      }

      // Filter by search query (invoice number or vendor name)
      if (_searchQuery.isNotEmpty) {
        return invoice.invoiceNumber.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            invoice.vendorId.toLowerCase().contains(_searchQuery.toLowerCase());
      }

      return true;
    }).toList();
  }

  /// Show filter dialog for selecting filter options
  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return InvoiceFilterDialog(
          showPaidOnly: _showPaidOnly,
          showUnpaidOnly: _showUnpaidOnly,
          selectedVendor: _selectedVendor,
          onApplyFilters: (showPaid, showUnpaid, vendor) {
            setState(() {
              _showPaidOnly = showPaid;
              _showUnpaidOnly = showUnpaid;
              _selectedVendor = vendor;
            });
          },
        );
      },
    );
  }
}
