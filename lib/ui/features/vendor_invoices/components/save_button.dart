import 'package:flutter/material.dart';
import 'package:we_like_money/screens/vendor_invoice/vendor_invoice_view_model.dart';
import 'package:we_like_money/ui/forms/form_section.dart';

/// A refactored button for saving the invoice
class SaveButtonRefactored extends StatelessWidget {
  /// The view model
  final VendorInvoiceViewModel viewModel;
  
  /// Callback when the save is successful
  final VoidCallback onSaveSuccess;
  
  /// Optional background color
  final Color? backgroundColor;
  
  /// Optional text color
  final Color? textColor;
  
  /// Optional button height
  final double? height;
  
  /// Constructor
  const SaveButtonRefactored({
    required this.viewModel,
    required this.onSaveSuccess,
    this.backgroundColor,
    this.textColor,
    this.height = 50,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: viewModel.isLoading,
      builder: (context, isLoading, _) {
        return SizedBox(
          width: double.infinity,
          height: height,
          child: ElevatedButton(
            key: const Value<PERSON>ey('saveButton'),
            onPressed: isLoading
                ? null
                : () async {
                    final success = await viewModel.saveInvoice(context);
                    if (success && context.mounted) {
                      onSaveSuccess();
                    }
                  },
            style: backgroundColor != null || textColor != null
                ? ElevatedButton.styleFrom(
                    backgroundColor: backgroundColor,
                    foregroundColor: textColor,
                  )
                : null,
            child: Text(viewModel.isEditMode ? 'Update Invoice' : 'Create Invoice'),
          ),
        );
      },
    );
  }
}

/// A form section wrapper for the save button
class SaveButtonSectionRefactored extends FormSection {
  /// The view model
  final VendorInvoiceViewModel viewModel;
  
  /// Callback when the save is successful
  final VoidCallback onSaveSuccess;
  
  /// Constructor
  const SaveButtonSectionRefactored({
    required this.viewModel,
    required this.onSaveSuccess,
    super.key,
  }) : super(
          title: '',
          showHeader: false,
        );
  
  @override
  Widget buildSectionContent(BuildContext context) {
    return SaveButtonRefactored(
      viewModel: viewModel,
      onSaveSuccess: onSaveSuccess,
      backgroundColor: Theme.of(context).primaryColor,
      textColor: Colors.white,
    );
  }
}
