import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:we_like_money/models/general_ledger.dart';
import 'package:we_like_money/models/ledger_entry.dart';
import 'package:we_like_money/ui/features/general_ledger/components/empty_ledger_view.dart';
import 'package:we_like_money/ui/features/general_ledger/components/error_view.dart';
import 'package:we_like_money/ui/features/general_ledger/components/filter_bar.dart';
import 'package:we_like_money/ui/features/general_ledger/components/ledger_table.dart';
import 'package:we_like_money/ui/features/general_ledger/components/loading_view.dart';
import 'package:we_like_money/ui/features/general_ledger/components/summary_card.dart';
import 'package:we_like_money/viewmodels/general_ledger_viewmodel.dart';

/// A screen that displays the general ledger with filtering capabilities
class GeneralLedgerScreen extends StatefulWidget {
  /// Constructor
  const GeneralLedgerScreen({super.key});

  @override
  State<GeneralLedgerScreen> createState() => _GeneralLedgerScreenState();
}

/// A screen that displays the general ledger with filtering capabilities (alias for backward compatibility)
@Deprecated('Use GeneralLedgerScreen instead')
class GeneralLedgerScreenRefactored extends StatefulWidget {
  /// Constructor
  const GeneralLedgerScreenRefactored({super.key});

  @override
  State<GeneralLedgerScreenRefactored> createState() =>
      _GeneralLedgerScreenRefactoredState();
}

class _GeneralLedgerScreenRefactoredState
    extends State<GeneralLedgerScreenRefactored> {
  @override
  Widget build(BuildContext context) {
    return const GeneralLedgerScreen();
  }
}

class _GeneralLedgerScreenState extends State<GeneralLedgerScreen> {
  final GeneralLedgerViewModel _viewModel =
      GetIt.instance<GeneralLedgerViewModel>();

  bool _isLoading = true;
  List<GeneralLedger> _entries = [];
  List<GeneralLedger> _filteredEntries = [];
  List<LedgerEntry> _ledgerEntries = [];
  String? _errorMessage;

  // Filter state
  String _searchQuery = '';
  String? _selectedAccountNumber;
  DateTime? _startDate;
  DateTime? _endDate;

  // Summary values
  double _totalDebits = 0;
  double _totalCredits = 0;
  double _netBalance = 0;

  // Formatters
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  // Unique account numbers for filtering
  List<String> _accountNumbers = [];

  @override
  void initState() {
    super.initState();
    _loadGeneralLedgerEntries();
  }

  Future<void> _loadGeneralLedgerEntries() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final entries = await _viewModel.getAllGeneralLedgerEntries();

      // Extract unique account numbers for filtering
      final accountNumbers = <String>{};
      for (final entry in entries) {
        accountNumbers.add(entry.accountNumber);
      }

      setState(() {
        _entries = entries;
        _filteredEntries = entries;
        _accountNumbers = accountNumbers.toList()..sort();
        _updateSummaryAndLedgerEntries();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _updateSummaryAndLedgerEntries() {
    double totalDebits = 0;
    double totalCredits = 0;
    final ledgerEntries = <LedgerEntry>[];
    double runningBalance = 0;

    // Sort entries by date
    final sortedEntries = List<GeneralLedger>.from(_filteredEntries)
      ..sort((a, b) => a.transactionDate.compareTo(b.transactionDate));

    for (final entry in sortedEntries) {
      totalDebits += entry.debit;
      totalCredits += entry.credit;

      // Calculate running balance
      runningBalance += entry.debit - entry.credit;

      // Create ledger entry
      final ledgerEntry = LedgerEntry.fromGeneralLedger(
        entry,
      ).copyWith(runningBalance: runningBalance);
      ledgerEntries.add(ledgerEntry);
    }

    setState(() {
      _totalDebits = totalDebits;
      _totalCredits = totalCredits;
      _netBalance = totalDebits - totalCredits;
      _ledgerEntries = ledgerEntries;
    });
  }

  void _applyFilters() {
    final filtered =
        _entries.where((entry) {
          // Filter by account number
          if (_selectedAccountNumber != null &&
              entry.accountNumber != _selectedAccountNumber) {
            return false;
          }

          // Filter by date range
          if (_startDate != null) {
            final startDate = DateTime(
              _startDate!.year,
              _startDate!.month,
              _startDate!.day,
            );
            if (entry.transactionDate.isBefore(startDate)) {
              return false;
            }
          }

          if (_endDate != null) {
            final endDate = DateTime(
              _endDate!.year,
              _endDate!.month,
              _endDate!.day,
              23,
              59,
              59,
            );
            if (entry.transactionDate.isAfter(endDate)) {
              return false;
            }
          }

          // Filter by search query
          if (_searchQuery.isNotEmpty) {
            final query = _searchQuery.toLowerCase();
            return entry.description.toLowerCase().contains(query) ||
                entry.accountNumber.toLowerCase().contains(query);
          }

          return true;
        }).toList();

    setState(() {
      _filteredEntries = filtered;
      _updateSummaryAndLedgerEntries();
    });
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedAccountNumber = null;
      _startDate = null;
      _endDate = null;
      _filteredEntries = _entries;
      _updateSummaryAndLedgerEntries();
    });
  }

  bool get _hasFilters =>
      _searchQuery.isNotEmpty ||
      _selectedAccountNumber != null ||
      _startDate != null ||
      _endDate != null;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('General Ledger'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGeneralLedgerEntries,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LedgerLoadingView();
    }

    if (_errorMessage != null) {
      return LedgerErrorView(
        errorMessage: _errorMessage!,
        onRetry: _loadGeneralLedgerEntries,
      );
    }

    return Column(
      children: [
        // Filter bar
        LedgerFilterBar(
          searchQuery: _searchQuery,
          selectedAccountNumber: _selectedAccountNumber,
          startDate: _startDate,
          endDate: _endDate,
          onSearchChanged: (value) {
            setState(() {
              _searchQuery = value;
              _applyFilters();
            });
          },
          onAccountNumberChanged: (value) {
            setState(() {
              _selectedAccountNumber = value;
              _applyFilters();
            });
          },
          onStartDateChanged: (value) {
            setState(() {
              _startDate = value;
              _applyFilters();
            });
          },
          onEndDateChanged: (value) {
            setState(() {
              _endDate = value;
              _applyFilters();
            });
          },
          onClearFilters: _clearFilters,
          accountNumbers: _accountNumbers,
          dateFormat: _dateFormat,
        ),

        // Ledger content
        Expanded(
          child:
              _filteredEntries.isEmpty
                  ? EmptyLedgerView(hasFilters: _hasFilters)
                  : ListView(
                    children: [
                      // Summary card
                      LedgerSummaryCard(
                        totalDebits: _totalDebits,
                        totalCredits: _totalCredits,
                        netBalance: _netBalance,
                      ),

                      // Ledger table
                      LedgerTable(
                        entries: _ledgerEntries,
                        dateFormat: _dateFormat,
                      ),
                    ],
                  ),
        ),
      ],
    );
  }
}
