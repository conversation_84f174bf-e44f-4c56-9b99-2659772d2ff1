import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:we_like_money/models/vendor.dart';
import 'package:we_like_money/providers/vendor_provider.dart';
import 'package:we_like_money/screens/vendor/vendor_detail_screen_refactored.dart';
import 'package:we_like_money/screens/vendor_invoice/vendor_invoice_entry_screen.dart';
import 'package:we_like_money/widgets/error_display.dart';

/// A screen that displays a list of vendors and allows the user to select one
/// to view or edit.
class VendorListScreen extends ConsumerStatefulWidget {
  const VendorListScreen({super.key});

  @override
  ConsumerState<VendorListScreen> createState() => _VendorListScreenState();
}

class _VendorListScreenState extends ConsumerState<VendorListScreen> {
  List<Vendor> _filteredVendors = [];
  String _searchQuery = '';
  bool _showActiveOnly = true;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _searchVendors() async {
    if (_searchQuery.isEmpty) {
      setState(() {
        _filteredVendors = ref.read(vendorsProvider).value ?? [];
      });
      return;
    }

    final query = _searchQuery.toLowerCase();
    setState(() {
      _filteredVendors =
          (ref.read(vendorsProvider).value ?? []).where((vendor) {
            if (_showActiveOnly && !vendor.isActive) {
              return false;
            }

            return vendor.vendorName.toLowerCase().contains(query) ||
                (vendor.vendorNumber?.toLowerCase().contains(query) ?? false) ||
                (vendor.organizationNumber?.toLowerCase().contains(query) ??
                    false);
          }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final vendorsAsync = ref.watch(vendorsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Vendors'),
        actions: [
          IconButton(
            icon: Icon(
              _showActiveOnly ? Icons.visibility : Icons.visibility_off,
            ),
            tooltip:
                _showActiveOnly ? 'Showing active only' : 'Showing all vendors',
            onPressed: () {
              setState(() {
                _showActiveOnly = !_showActiveOnly;
              });
              _searchVendors();
            },
          ),
          IconButton(
            icon: const Icon(Icons.receipt),
            tooltip: 'Create Vendor Invoice',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const VendorInvoiceEntryScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Vendor',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const VendorDetailScreenRefactored(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'Search vendors',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _searchVendors();
              },
            ),
          ),
          Expanded(
            child: vendorsAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) {
                return ErrorDisplay(
                  errorMessage: error.toString(),
                  onRetry: () => ref.refresh(vendorsProvider),
                );
              },
              data: (vendors) {
                // Initialize filtered vendors if not done yet
                if (_filteredVendors.isEmpty && vendors.isNotEmpty) {
                  _filteredVendors =
                      _showActiveOnly
                          ? vendors.where((v) => v.isActive).toList()
                          : vendors;
                }

                if (_filteredVendors.isEmpty) {
                  return Center(
                    child:
                        vendors.isEmpty
                            ? const Text(
                              'No vendors found. Create a vendor to get started.',
                            )
                            : const Text(
                              'No vendors match the current filters.',
                            ),
                  );
                }

                return ListView.builder(
                  itemCount: _filteredVendors.length,
                  itemBuilder: (context, index) {
                    final vendor = _filteredVendors[index];
                    return _buildVendorListItem(vendor);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVendorListItem(Vendor vendor) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: vendor.isActive ? Colors.green : Colors.grey,
          child: const Icon(Icons.business),
        ),
        title: Text(
          vendor.vendorName,
          style: TextStyle(
            color: vendor.isActive ? Colors.black : Colors.grey,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (vendor.vendorNumber != null)
              Text('Vendor #: ${vendor.vendorNumber}'),
            if (vendor.phone != null) Text('Phone: ${vendor.phone}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => VendorDetailScreenRefactored(vendor: vendor),
                ),
              ).then((_) => ref.refresh(vendorsProvider));
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem<String>(value: 'edit', child: Text('Edit')),
              ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => VendorDetailScreenRefactored(vendor: vendor),
            ),
          ).then((_) => ref.refresh(vendorsProvider));
        },
      ),
    );
  }
}
