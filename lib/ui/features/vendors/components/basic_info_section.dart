import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/form_section.dart';
import 'package:we_like_money/ui/forms/fields/text_form_field.dart';
import 'package:we_like_money/utils/validation/index.dart';

/// A form section for vendor basic information
class BasicInfoSection extends FormSection {
  /// The vendor name controller
  final TextEditingController vendorNameController;

  /// The vendor number controller
  final TextEditingController vendorNumberController;

  /// The organization number controller
  final TextEditingController orgNumberController;

  /// Whether the vendor is active
  final bool isActive;

  /// Callback when the active status changes
  final ValueChanged<bool> onActiveChanged;

  /// Constructor
  const BasicInfoSection({
    required this.vendorNameController,
    required this.vendorNumberController,
    required this.orgNumberController,
    required this.isActive,
    required this.onActiveChanged,
    super.key,
  }) : super(title: 'Basic Information', headerIcon: Icons.business);

  @override
  Widget buildSectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Active status
        SwitchListTile(
          title: const Text('Active'),
          value: isActive,
          onChanged: onActiveChanged,
        ),
        const SizedBox(height: 16),

        // Vendor name
        AppTextFormField(
          labelText: 'Vendor Name',
          value: vendorNameController.text,
          onChanged: (value) => vendorNameController.text = value,
          hintText: 'Enter vendor name',
          isRequired: true,
          validationRule: Validator.required('Please enter a vendor name'),
          prefixIcon: Icons.business,
        ),
        const SizedBox(height: 8),

        // Vendor number
        AppTextFormField(
          labelText: 'Vendor Number',
          value: vendorNumberController.text,
          onChanged: (value) => vendorNumberController.text = value,
          hintText: 'Enter vendor number',
          prefixIcon: Icons.numbers,
        ),
        const SizedBox(height: 8),

        // Organization number
        AppTextFormField(
          labelText: 'Organization Number',
          value: orgNumberController.text,
          onChanged: (value) => orgNumberController.text = value,
          hintText: 'Enter organization number',
          prefixIcon: Icons.corporate_fare,
        ),
      ],
    );
  }
}
