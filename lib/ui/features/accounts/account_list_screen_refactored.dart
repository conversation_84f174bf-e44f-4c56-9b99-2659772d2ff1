import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/screens/account/components/account_list_item.dart';
import 'package:we_like_money/screens/account/components/account_search_bar.dart';
import 'package:we_like_money/screens/account/components/empty_accounts_view.dart';
import 'package:we_like_money/screens/account/components/error_view.dart';
import 'package:we_like_money/screens/account/components/loading_view.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

/// A refactored screen that displays a list of accounts and allows the user to select one
/// to view its register.
class AccountListScreenRefactored extends StatefulWidget {
  /// Constructor
  const AccountListScreenRefactored({super.key});

  @override
  State<AccountListScreenRefactored> createState() => _AccountListScreenRefactoredState();
}

class _AccountListScreenRefactoredState extends State<AccountListScreenRefactored> {
  final AccountViewModel _viewModel = GetIt.instance<AccountViewModel>();
  bool _isLoading = true;
  List<Account> _accounts = [];
  List<Account> _filteredAccounts = [];
  Map<String, double> _accountBalances = {};
  String? _errorMessage;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final accounts = await _viewModel.getAccounts();

      // Load account balances one by one
      final balances = <String, double>{};
      for (final account in accounts) {
        balances[account.accountNumber] = await _viewModel
            .calculateAccountBalance(account.accountNumber);
      }

      setState(() {
        _accounts = accounts;
        _filteredAccounts = accounts;
        _accountBalances = balances;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterAccounts(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredAccounts = _accounts;
      } else {
        _filteredAccounts = _accounts.where((account) {
          final accountName = account.accountName.toLowerCase();
          final accountNumber = account.accountNumber.toLowerCase();
          final searchLower = query.toLowerCase();
          return accountName.contains(searchLower) || 
                 accountNumber.contains(searchLower);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAccounts,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Implement account creation
        },
        tooltip: 'Add Account',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingView();
    }

    if (_errorMessage != null) {
      return ErrorView(
        errorMessage: _errorMessage!,
        onRetry: _loadAccounts,
      );
    }

    return Column(
      children: [
        AccountSearchBar(
          searchQuery: _searchQuery,
          onSearchChanged: _filterAccounts,
        ),
        Expanded(
          child: _filteredAccounts.isEmpty
              ? const EmptyAccountsView()
              : _buildAccountList(),
        ),
      ],
    );
  }

  Widget _buildAccountList() {
    return ListView.builder(
      itemCount: _filteredAccounts.length,
      itemBuilder: (context, index) {
        final account = _filteredAccounts[index];
        final balance = _accountBalances[account.accountNumber] ?? 0.0;
        return AccountListItem(
          account: account,
          balance: balance,
        );
      },
    );
  }
}
