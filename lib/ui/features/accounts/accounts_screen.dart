import 'package:flutter/material.dart';
import 'package:we_like_money/di/injection.dart';
import 'package:we_like_money/ui/screens/account_details_screen.dart';
import 'package:we_like_money/models/account.dart';
import 'package:we_like_money/utils/exceptions.dart';
import 'package:we_like_money/viewmodels/account_viewmodel.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  // Get the AccountViewModel from the dependency injection container
  final AccountViewModel _viewModel = getIt<AccountViewModel>();
  List<Account> _accounts = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final accounts = await _viewModel.getAccounts();
      setState(() {
        _accounts = accounts;
        _isLoading = false;
      });
    } on BusinessException catch (e) {
      setState(() {
        _error = e.message;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'An unexpected error occurred';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error: $_error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadAccounts,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _accounts.isEmpty
              ? const Center(child: Text('No accounts found'))
              : ListView.builder(
                itemCount: _accounts.length,
                itemBuilder: (context, index) {
                  final account = _accounts[index];
                  return ListTile(
                    title: Text(account.accountName),
                    subtitle: Text(account.accountNumber),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => AccountDetailsScreen(
                                accountNumber: account.accountNumber,
                              ),
                        ),
                      );
                    },
                  );
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add account screen
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
