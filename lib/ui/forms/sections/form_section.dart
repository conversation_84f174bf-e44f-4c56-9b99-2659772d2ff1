import 'package:flutter/material.dart';
import 'package:we_like_money/ui/forms/form_section_header.dart';

/// A base class for form sections that provides common functionality
abstract class FormSection extends StatelessWidget {
  /// The title of the section
  final String title;
  
  /// Whether to show the section header
  final bool showHeader;
  
  /// Spacing between the header and content
  final double headerSpacing;
  
  /// Spacing after the section
  final double bottomSpacing;
  
  /// Whether the section is visible
  final bool isVisible;
  
  /// Optional help text for the section
  final String? helpText;
  
  /// Optional icon for the section header
  final IconData? headerIcon;
  
  /// Constructor
  const FormSection({
    required this.title,
    this.showHeader = true,
    this.headerSpacing = 8.0,
    this.bottomSpacing = 24.0,
    this.isVisible = true,
    this.helpText,
    this.headerIcon,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: EdgeInsets.only(bottom: bottomSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showHeader) ...[
            FormSectionHeader(
              title: title,
              helpText: helpText,
              icon: headerIcon,
            ),
            SizedBox(height: headerSpacing),
          ],
          buildSectionContent(context),
        ],
      ),
    );
  }
  
  /// Build the content of the section
  /// 
  /// This method must be implemented by subclasses
  Widget buildSectionContent(BuildContext context);
}
