import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// A reusable date picker form field
class DatePickerFormField extends StatelessWidget {
  final DateTime value;
  final ValueChanged<DateTime> onChanged;
  final String label;
  final String? Function(DateTime?)? validator;

  const DatePickerFormField({
    super.key,
    required this.value,
    required this.onChanged,
    required this.label,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return FormField<DateTime>(
      validator: validator,
      builder: (FormFieldState<DateTime> field) {
        return InkWell(
          onTap: () async {
            final pickedDate = await showDatePicker(
              context: context,
              initialDate: value,
              firstDate: DateTime(2000),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );

            if (pickedDate != null) {
              onChanged(pickedDate);
              field.didChange(pickedDate);
            }
          },
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: label,
              border: const OutlineInputBorder(),
              errorText: field.errorText,
            ),
            child: Text(DateFormat('yyyy-MM-dd').format(value)),
          ),
        );
      },
    );
  }
}

/// A reusable currency amount form field
class CurrencyAmountFormField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String? Function(String?)? validator;

  const CurrencyAmountFormField({
    super.key,
    required this.controller,
    required this.label,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: 'Enter amount',
        border: const OutlineInputBorder(),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
      ],
      validator: validator,
    );
  }
}

/// A reusable dropdown form field
class DropdownFormField<T> extends StatelessWidget {
  final T? value;
  final List<T> items;
  final ValueChanged<T?> onChanged;
  final String label;
  final String Function(T) displayString;
  final String? Function(T?)? validator;

  const DropdownFormField({
    super.key,
    required this.value,
    required this.items,
    required this.onChanged,
    required this.label,
    required this.displayString,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      value: value,
      items:
          items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(displayString(item)),
            );
          }).toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }
}

/// A reusable optional dropdown form field
class OptionalDropdownFormField<T> extends StatelessWidget {
  final T? value;
  final List<T> items;
  final ValueChanged<T?> onChanged;
  final String label;
  final String Function(T) displayString;

  const OptionalDropdownFormField({
    super.key,
    required this.value,
    required this.items,
    required this.onChanged,
    required this.label,
    required this.displayString,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T?>(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      value: value,
      items: [
        DropdownMenuItem<T?>(value: null, child: const Text('-- None --')),
        ...items.map((item) {
          return DropdownMenuItem<T?>(
            value: item,
            child: Text(displayString(item)),
          );
        }),
      ],
      onChanged: onChanged,
    );
  }
}
