#!/bin/bash

# Create symbolic links to maintain backward compatibility

echo "Creating symbolic links to maintain backward compatibility..."

# Create symbolic links for lib/screens
mkdir -p lib/screens/account/components
mkdir -p lib/screens/general_ledger/components
mkdir -p lib/screens/vendor/components
mkdir -p lib/screens/vendor_invoice/components

# Account
ln -sf ../../ui/features/accounts/account_list_screen.dart lib/screens/account/
ln -sf ../../ui/features/accounts/account_list_screen_refactored.dart lib/screens/account/
ln -sf ../../ui/features/accounts/account_register_screen.dart lib/screens/account/
ln -sf ../../../ui/features/accounts/components/account_list_item.dart lib/screens/account/components/
ln -sf ../../../ui/features/accounts/components/account_search_bar.dart lib/screens/account/components/
ln -sf ../../../ui/features/accounts/components/empty_accounts_view.dart lib/screens/account/components/
ln -sf ../../../ui/features/accounts/components/error_view.dart lib/screens/account/components/
ln -sf ../../../ui/features/accounts/components/loading_view.dart lib/screens/account/components/

# General Ledger
ln -sf ../../ui/features/general_ledger/general_ledger_screen.dart lib/screens/general_ledger/general_ledger_screen_refactored.dart
ln -sf ../../../ui/features/general_ledger/components/empty_ledger_view.dart lib/screens/general_ledger/components/
ln -sf ../../../ui/features/general_ledger/components/error_view.dart lib/screens/general_ledger/components/
ln -sf ../../../ui/features/general_ledger/components/filter_bar.dart lib/screens/general_ledger/components/
ln -sf ../../../ui/features/general_ledger/components/ledger_table.dart lib/screens/general_ledger/components/
ln -sf ../../../ui/features/general_ledger/components/loading_view.dart lib/screens/general_ledger/components/
ln -sf ../../../ui/features/general_ledger/components/summary_card.dart lib/screens/general_ledger/components/

# Vendor
ln -sf ../../ui/features/vendors/vendor_detail_screen.dart lib/screens/vendor/
ln -sf ../../ui/features/vendors/vendor_detail_screen_refactored.dart lib/screens/vendor/
ln -sf ../../ui/features/vendors/vendor_list_screen.dart lib/screens/vendor/
ln -sf ../../ui/features/vendors/vendor_quick_create_dialog.dart lib/screens/vendor/
ln -sf ../../../ui/features/vendors/components/address_section.dart lib/screens/vendor/components/
ln -sf ../../../ui/features/vendors/components/banking_info_section.dart lib/screens/vendor/components/
ln -sf ../../../ui/features/vendors/components/basic_info_section.dart lib/screens/vendor/components/
ln -sf ../../../ui/features/vendors/components/contact_info_section.dart lib/screens/vendor/components/
ln -sf ../../../ui/features/vendors/components/save_button_section.dart lib/screens/vendor/components/

# Vendor Invoice
ln -sf ../../ui/features/vendor_invoices/vendor_invoice_detail_screen.dart lib/screens/vendor_invoice/
ln -sf ../../ui/features/vendor_invoices/vendor_invoice_entry_screen.dart lib/screens/vendor_invoice/
ln -sf ../../ui/features/vendor_invoices/vendor_invoice_form_state.dart lib/screens/vendor_invoice/
ln -sf ../../ui/features/vendor_invoices/vendor_invoice_list_screen.dart lib/screens/vendor_invoice/
ln -sf ../../ui/features/vendor_invoices/vendor_invoice_view_model.dart lib/screens/vendor_invoice/
ln -sf ../../../ui/features/vendor_invoices/components/accounting_section.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/invoice_details_section.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/invoice_filter_dialog.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/invoice_list_item.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/invoice_search_bar.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/optional_fields_section.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/save_button.dart lib/screens/vendor_invoice/components/
ln -sf ../../../ui/features/vendor_invoices/components/vendor_selection_section.dart lib/screens/vendor_invoice/components/

# Create symbolic links for lib/ui/screens
mkdir -p lib/ui/screens/expense/components/refactored

# Expense
ln -sf ../../ui/features/expenses/expense_entry_screen.dart lib/ui/screens/expense/
ln -sf ../../ui/features/expenses/expense_entry_screen_refactored.dart lib/ui/screens/expense/
ln -sf ../../ui/features/expenses/expense_form_state.dart lib/ui/screens/expense/
ln -sf ../../ui/features/expenses/expense_view_model.dart lib/ui/screens/expense/
ln -sf ../../../ui/features/expenses/components/expense_details_section.dart lib/ui/screens/expense/components/
ln -sf ../../../ui/features/expenses/components/form_section_header.dart lib/ui/screens/expense/components/
ln -sf ../../../ui/features/expenses/components/payment_details_section.dart lib/ui/screens/expense/components/
ln -sf ../../../ui/features/expenses/components/project_staff_section.dart lib/ui/screens/expense/components/
ln -sf ../../../ui/features/expenses/components/save_button.dart lib/ui/screens/expense/components/
ln -sf ../../../ui/features/expenses/components/vendor_selection_section.dart lib/ui/screens/expense/components/
ln -sf ../../../../ui/features/expenses/components/refactored/expense_details_section.dart lib/ui/screens/expense/components/refactored/
ln -sf ../../../../ui/features/expenses/components/refactored/payment_details_section.dart lib/ui/screens/expense/components/refactored/
ln -sf ../../../../ui/features/expenses/components/refactored/project_staff_section.dart lib/ui/screens/expense/components/refactored/
ln -sf ../../../../ui/features/expenses/components/refactored/vendor_section.dart lib/ui/screens/expense/components/refactored/
ln -sf ../../ui/features/expenses/expenses_screen.dart lib/ui/screens/

# Other screens
ln -sf ../../ui/features/home/<USER>/ui/screens/
ln -sf ../../ui/features/settings/settings_screen.dart lib/ui/screens/
ln -sf ../../ui/features/companies/companies_screen.dart lib/ui/screens/
ln -sf ../../ui/features/projects/projects_screen.dart lib/ui/screens/
ln -sf ../../ui/features/accounts/account_details_screen.dart lib/ui/screens/

# Create symbolic links for lib/widgets
mkdir -p lib/widgets
mkdir -p lib/ui/widgets

# Widgets
ln -sf ../ui/common/widgets/custom_dropdown.dart lib/widgets/
ln -sf ../ui/common/widgets/date_picker_field.dart lib/widgets/
ln -sf ../ui/common/widgets/error_display.dart lib/widgets/
ln -sf ../ui/common/widgets/loading_display.dart lib/widgets/
ln -sf ../../ui/common/widgets/company_selector.dart lib/ui/widgets/
ln -sf ../../ui/common/widgets/connection_status.dart lib/ui/widgets/
ln -sf ../../ui/common/widgets/currency_display.dart lib/ui/widgets/
ln -sf ../../ui/common/widgets/form_fields.dart lib/ui/widgets/

# Create symbolic links for lib/ui/forms
ln -sf sections/form_section.dart lib/ui/forms/form_section.dart
ln -sf sections/form_section_header.dart lib/ui/forms/form_section_header.dart

echo "Symbolic links created successfully."
