# Form Validation System Documentation

WeLikeMoney implements a comprehensive form validation system with multiple specialized validator classes to ensure data integrity and a consistent user experience across the application.

## Available Validator Classes

- [`FormValidators`](#formvalidators) - Core validation utilities for general form fields
- [`DateValidators`](#datevalidators) - Date-specific validation utilities
- [`InvoiceValidators`](#invoicevalidators) - Validation utilities for invoice fields
- [`CurrencyValidators`](#currencyvalidators) - Validation utilities for currency-related fields
- [`Validator`](#validator-composite) - A composite API that provides access to all validators with a cleaner syntax

## FormValidators

The `FormValidators` class provides foundational validation methods for form fields:

```dart
import 'package:we_like_money/utils/form_validators.dart';

// Example usage
final nameValidator = FormValidators.required('Name is required');
final emailValidator = FormValidators.email();
final passwordValidator = FormValidators.password(
  minLength: 10,
  requireSpecialChars: true,
);
```

### Available Methods

| Method | Description |
|--------|-------------|
| `required(String errorMessage)` | Validates that a field is not empty |
| `email([String? errorMessage])` | Validates that a field contains a valid email address |
| `minLength(int minLength, String errorMessage)` | Validates that a field meets a minimum length |
| `maxLength(int maxLength, String errorMessage)` | Validates that a field does not exceed a maximum length |
| `numeric([String? errorMessage])` | Validates that a field contains a valid numeric value |
| `pattern(RegExp pattern, String errorMessage)` | Validates that a field matches a specific regular expression |
| `password({...})` | Validates password complexity with configurable requirements |
| `money({...})` | Validates monetary amounts with configurable constraints |
| `matches(String Function() getCompareValue, [String errorMessage])` | Ensures two fields match (e.g., password confirmation) |
| `combine(List<String? Function(String?)> validators)` | Combines multiple validators and returns the first error message |

## DateValidators

The `DateValidators` class provides validation methods specifically for date fields:

```dart
import 'package:we_like_money/utils/date_validators.dart';

// Example usage
final dateFormatValidator = DateValidators.format('yyyy-MM-dd');
final dueDateValidator = DateValidators.after(invoiceDate, 'yyyy-MM-dd');
final businessDayValidator = DateValidators.businessDay(
  'yyyy-MM-dd',
  holidays: holidays,
);
```

### Available Methods

| Method | Description |
|--------|-------------|
| `format(String format, [String? errorMessage])` | Validates that a date string follows a specified format |
| `after(DateTime minDate, String dateFormat, [String? errorMessage, bool inclusive])` | Validates that a date occurs after a specified date |
| `before(DateTime maxDate, String dateFormat, [String? errorMessage, bool inclusive])` | Validates that a date occurs before a specified date |
| `between(DateTime minDate, DateTime maxDate, String dateFormat, [String? errorMessage, bool inclusiveMin, bool inclusiveMax])` | Validates that a date falls within a range |
| `notWeekend(String dateFormat, [String errorMessage])` | Validates that a date is not a weekend day |
| `businessDay(String dateFormat, {List<DateTime> holidays, String errorMessage})` | Validates that a date is a business day (not a weekend or holiday) |
| `dateRange(DateTime startDate, DateTime endDate, String format, {...})` | Validates that a date is within a specified range with configurable inclusivity |

## InvoiceValidators

The `InvoiceValidators` class provides specialized validation for invoice-related fields:

```dart
import 'package:we_like_money/utils/invoice_validators.dart';

// Example usage
final invoiceNumberValidator = InvoiceValidators.invoiceNumber();
final amountValidator = InvoiceValidators.amount(minAmount: 1.0);
final vendorValidator = InvoiceValidators.vendorSelected();
```

### Available Methods

| Method | Description |
|--------|-------------|
| `invoiceNumber({RegExp? pattern, String? errorMessage, int maxLength})` | Validates invoice numbers format and length |
| `dueDateValidator(DateTime invoiceDate, {...})` | Validates due dates relative to invoice dates |
| `amount({double minAmount, double? maxAmount, bool allowZero, String? errorMessage})` | Validates invoice amounts with configurable constraints |
| `taxAmount(double Function() getInvoiceAmount, {...})` | Validates tax amounts relative to invoice amounts |
| `vendorSelected([String errorMessage])` | Validates that a vendor has been selected |
| `accountSelected([String errorMessage])` | Validates that an expense account has been selected |

## CurrencyValidators

The `CurrencyValidators` class provides validation for currency-related fields:

```dart
import 'package:we_like_money/utils/currency_validators.dart';

// Example usage
final codeValidator = CurrencyValidators.currencyCode();
final rateValidator = CurrencyValidators.exchangeRate();
final symbolValidator = CurrencyValidators.currencySymbol();
```

### Available Methods

| Method | Description |
|--------|-------------|
| `currencyCode([String? errorMessage])` | Validates currency codes per ISO 4217 standard (3 uppercase letters) |
| `currencyName({int maxLength, String? errorMessage})` | Validates currency names with configurable length constraints |
| `exchangeRate({double minRate, double? maxRate, String? errorMessage})` | Validates exchange rates with configurable range |
| `targetCurrencyNotSameAsBase(String Function() getBaseCurrencyCode, [String errorMessage])` | Ensures base and target currencies differ |
| `currencySymbol({int maxLength, String? errorMessage})` | Validates currency symbols with configurable length |
| `decimalPlaces([String? errorMessage])` | Validates decimal places are between 0 and 6 |

## Validator (Composite)

The `Validator` class provides a unified API for accessing all validation methods:

```dart
import 'package:we_like_money/utils/composite_validator.dart';

// Example usage
final emailValidator = Validator.email();
final passwordValidator = Validator.password(minLength: 10);
final dateValidator = Validator.dateFormat('yyyy-MM-dd');

// Compose multiple validators
final combinedValidator = Validator.compose([
  Validator.required(),
  Validator.email(),
  Validator.maxLength(100),
]);
```

This class wraps all methods from `FormValidators` and `DateValidators` with a cleaner API.

## Integration with Flutter Forms

These validators can be used directly with Flutter's `TextFormField`:

```dart
TextFormField(
  decoration: InputDecoration(labelText: 'Email'),
  validator: FormValidators.email(),
  onSaved: (value) => email = value,
),

TextFormField(
  decoration: InputDecoration(labelText: 'Due Date'),
  validator: Validator.compose([
    Validator.required('Due date is required'),
    Validator.dateFormat('yyyy-MM-dd'),
    Validator.businessDay('yyyy-MM-dd'),
  ]),
  onSaved: (value) => dueDate = value,
),
```

## Best Practices

1. **Combine Validators**: Use `FormValidators.combine()` or `Validator.compose()` to chain multiple validation rules
2. **Custom Error Messages**: Provide clear, user-friendly error messages
3. **Reuse Validators**: Create reusable validator configurations for common fields
4. **Validation Groups**: Group related validators together for complex forms
5. **Contextual Validation**: Use validators that relate fields to each other (e.g., `dueDateValidator` comparing to invoice date) 